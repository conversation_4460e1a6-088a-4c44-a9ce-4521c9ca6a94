<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>octomap_server2</name>
  <version>0.0.0</version>
  <description>Octomap server for Ros2</description>
  <maintainer email="k<PERSON><PERSON><PERSON>@krish.neel"><PERSON><PERSON><PERSON></maintainer>
  <license>TODO: License declaration</license>

 <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>rclcpp</build_depend>
  <build_depend>rclcpp_components</build_depend>
  <build_depend>rcutils</build_depend>
  <build_depend>rmw</build_depend>
  <build_depend>rmw_implementation_cmake</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>pcl_conversions</build_depend>
  <build_depend>pcl_msgs</build_depend>
  <build_depend>octomap_msgs</build_depend>

  <exec_depend>launch_ros</exec_depend>
  <exec_depend>launch_xml</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>rclcpp_components</exec_depend>
  <exec_depend>rcutils</exec_depend>
  <exec_depend>rmw</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <exec_depend>pcl_msgs</exec_depend>
  <exec_depend>octomap_msgs</exec_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
