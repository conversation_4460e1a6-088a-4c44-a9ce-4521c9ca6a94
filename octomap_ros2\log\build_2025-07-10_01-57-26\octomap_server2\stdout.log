-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rclcpp_components: 16.0.12 (/opt/ros/humble/share/rclcpp_components/cmake)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- FLANN found (include: /usr/include, lib: /usr/lib/aarch64-linux-gnu/libflann_cpp.so)
-- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") 
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- looking for PCL_COMMON
-- looking for PCL_KDTREE
-- looking for PCL_OCTREE
-- looking for PCL_SEARCH
-- looking for PCL_SAMPLE_CONSENSUS
-- looking for PCL_FILTERS
-- looking for PCL_2D
-- looking for PCL_GEOMETRY
-- looking for PCL_IO
-- looking for PCL_FEATURES
-- looking for PCL_ML
-- looking for PCL_SEGMENTATION
-- looking for PCL_VISUALIZATION
-- looking for PCL_SURFACE
-- looking for PCL_REGISTRATION
-- looking for PCL_KEYPOINTS
-- looking for PCL_TRACKING
-- looking for PCL_RECOGNITION
-- looking for PCL_STEREO
-- looking for PCL_APPS
-- looking for PCL_IN_HAND_SCANNER
-- looking for PCL_MODELER
-- looking for PCL_POINT_CLOUD_EDITOR
-- looking for PCL_OUTOFCORE
-- looking for PCL_PEOPLE
-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)
-- Found std_srvs: 4.8.0 (/opt/ros/humble/share/std_srvs/cmake)
-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
-- Found octomap_msgs: 2.0.1 (/opt/ros/humble/share/octomap_msgs/cmake)
-- Found tf2: 0.25.13 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_ros: 0.25.13 (/opt/ros/humble/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Configuring done (5.7s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[ 33%] Built target octomap_server
[100%] Built target octomap_server2
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake
-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml
