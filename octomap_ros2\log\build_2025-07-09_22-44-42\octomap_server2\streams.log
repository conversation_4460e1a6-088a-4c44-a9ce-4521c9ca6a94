[0.020s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[0.189s] -- The C compiler identification is GNU 11.4.0
[0.331s] -- The CXX compiler identification is GNU 11.4.0
[0.381s] -- Detecting C compiler ABI info
[0.524s] -- Detecting C compiler ABI info - done
[0.557s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.559s] -- Detecting C compile features
[0.561s] -- Detecting C compile features - done
[0.587s] -- Detecting CXX compiler <PERSON><PERSON> info
[0.741s] -- Detecting CXX compiler <PERSON><PERSON> info - done
[0.772s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.773s] -- Detecting CXX compile features
[0.774s] -- Detecting CXX compile features - done
[0.784s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[1.101s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[1.249s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[1.347s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[1.466s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.478s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.501s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.540s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.593s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.723s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.730s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.912s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")  
[1.979s] -- Found FastRTPS: /opt/ros/humble/include  
[2.071s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[2.093s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[2.209s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[2.212s] -- Found Threads: TRUE  
[2.413s] -- Found rclcpp_components: 16.0.12 (/opt/ros/humble/share/rclcpp_components/cmake)
[2.502s] [33mCMake Warning (dev) at /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:146 (find_package):
[2.502s]   Policy CMP0144 is not set: find_package uses upper-case <PACKAGENAME>_ROOT
[2.503s]   variables.  Run "cmake --help-policy CMP0144" for policy details.  Use the
[2.503s]   cmake_policy command to set the policy and suppress this warning.
[2.503s] 
[2.503s]   CMake variable EIGEN_ROOT is set to:
[2.503s] 
[2.503s]     /usr/include/eigen3
[2.503s] 
[2.503s]   For compatibility, find_package is ignoring the variable, but code in a
[2.504s]   .cmake module might still use it.
[2.504s] Call Stack (most recent call first):
[2.504s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:299 (find_eigen)
[2.504s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:543 (find_external_library)
[2.504s]   CMakeLists.txt:21 (find_package)
[2.504s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.504s] [0m
[2.511s] -- Checking for module 'eigen3'
[2.537s] --   Found eigen3, version 3.4.0
[2.592s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[2.592s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[2.652s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found suitable version "1.74.0", minimum required is "1.65.0") found components: system filesystem date_time iostreams serialization 
[2.657s] [33mCMake Warning (dev) at /usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindFLANN.cmake:44 (find_package):
[2.658s]   Policy CMP0144 is not set: find_package uses upper-case <PACKAGENAME>_ROOT
[2.658s]   variables.  Run "cmake --help-policy CMP0144" for policy details.  Use the
[2.658s]   cmake_policy command to set the policy and suppress this warning.
[2.658s] 
[2.659s]   CMake variable FLANN_ROOT is set to:
[2.659s] 
[2.659s]     /usr
[2.659s] 
[2.659s]   For compatibility, find_package is ignoring the variable, but code in a
[2.659s]   .cmake module might still use it.
[2.659s] Call Stack (most recent call first):
[2.660s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:256 (find_package)
[2.660s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:301 (find_flann)
[2.660s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:543 (find_external_library)
[2.660s]   CMakeLists.txt:21 (find_package)
[2.660s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.660s] [0m
[2.686s] -- Checking for module 'flann'
[2.709s] --   Found flann, version 1.9.1
[2.794s] -- Found FLANN: /usr/lib/aarch64-linux-gnu/libflann_cpp.so  
[2.794s] -- FLANN found (include: /usr/include, lib: /usr/lib/aarch64-linux-gnu/libflann_cpp.so)
[2.902s] -- Found GLEW: /usr/lib/aarch64-linux-gnu/libGLEW.so  
[2.916s] -- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so  found components: OpenGL GLX 
[3.197s] -- Found MPI_C: /usr/lib/aarch64-linux-gnu/libmpi.so (found version "3.1") 
[3.198s] -- Found MPI: TRUE (found version "3.1") found components: C 
[3.209s] -- Found JsonCpp: /usr/lib/aarch64-linux-gnu/libjsoncpp.so (found suitable version "1.9.5", minimum required is "0.7.0") 
[3.262s] -- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")  
[3.332s] -- Found PNG: /usr/lib/aarch64-linux-gnu/libpng.so (found version "1.6.37") 
[3.382s] -- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") 
[3.636s] -- Found X11: /usr/include   
[3.638s] -- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
[3.764s] -- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
[3.764s] -- Looking for gethostbyname
[3.903s] -- Looking for gethostbyname - found
[3.904s] -- Looking for connect
[4.024s] -- Looking for connect - found
[4.025s] -- Looking for remove
[4.156s] -- Looking for remove - found
[4.157s] -- Looking for shmat
[4.284s] -- Looking for shmat - found
[4.284s] -- Looking for IceConnectionNumber in ICE
[4.414s] -- Looking for IceConnectionNumber in ICE - found
[4.652s] -- Found EXPAT: /usr/lib/aarch64-linux-gnu/libexpat.so (found version "2.4.7") 
[4.659s] -- Found double-conversion: /usr/lib/aarch64-linux-gnu/libdouble-conversion.so  
[4.669s] -- Found LZ4: /usr/lib/aarch64-linux-gnu/liblz4.so (found version "1.9.3") 
[4.676s] -- Found LZMA: /usr/lib/aarch64-linux-gnu/liblzma.so (found version "5.2.5") 
[4.742s] -- Found JPEG: /usr/lib/aarch64-linux-gnu/libjpeg.so (found version "80") 
[4.760s] -- Found TIFF: /usr/lib/aarch64-linux-gnu/libtiff.so (found version "4.3.0")  
[4.778s] -- Found Freetype: /usr/lib/aarch64-linux-gnu/libfreetype.so (found version "2.11.1") 
[4.793s] -- Found utf8cpp: /usr/include/utf8cpp  
[4.960s] -- Checking for module 'libusb-1.0'
[4.984s] --   Found libusb-1.0, version 1.0.25
[5.061s] -- Found libusb: /usr/lib/aarch64-linux-gnu/libusb-1.0.so  
[5.066s] -- Found OpenNI: /usr/lib/libOpenNI.so;libusb::libusb (found version "1.5.4.0") 
[5.066s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[5.190s] -- Found OpenNI2: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb (found version "2.2.0.33") 
[5.190s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
[5.190s] [0m** WARNING ** io features related to pcap will be disabled[0m
[5.746s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[5.760s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[5.773s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
[6.282s] -- Found Qhull version 8.0.2
[6.805s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[7.342s] -- looking for PCL_COMMON
[7.345s] -- Found PCL_COMMON: /usr/lib/aarch64-linux-gnu/libpcl_common.so  
[7.345s] -- looking for PCL_KDTREE
[7.347s] -- Found PCL_KDTREE: /usr/lib/aarch64-linux-gnu/libpcl_kdtree.so  
[7.347s] -- looking for PCL_OCTREE
[7.349s] -- Found PCL_OCTREE: /usr/lib/aarch64-linux-gnu/libpcl_octree.so  
[7.350s] -- looking for PCL_SEARCH
[7.353s] -- Found PCL_SEARCH: /usr/lib/aarch64-linux-gnu/libpcl_search.so  
[7.353s] -- looking for PCL_SAMPLE_CONSENSUS
[7.356s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/aarch64-linux-gnu/libpcl_sample_consensus.so  
[7.356s] -- looking for PCL_FILTERS
[7.359s] -- Found PCL_FILTERS: /usr/lib/aarch64-linux-gnu/libpcl_filters.so  
[7.360s] -- looking for PCL_2D
[7.361s] -- Found PCL_2D: /usr/include/pcl-1.12  
[7.361s] -- looking for PCL_GEOMETRY
[7.362s] -- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
[7.362s] -- looking for PCL_IO
[7.365s] -- Found PCL_IO: /usr/lib/aarch64-linux-gnu/libpcl_io.so  
[7.365s] -- looking for PCL_FEATURES
[7.367s] -- Found PCL_FEATURES: /usr/lib/aarch64-linux-gnu/libpcl_features.so  
[7.368s] -- looking for PCL_ML
[7.370s] -- Found PCL_ML: /usr/lib/aarch64-linux-gnu/libpcl_ml.so  
[7.371s] -- looking for PCL_SEGMENTATION
[7.373s] -- Found PCL_SEGMENTATION: /usr/lib/aarch64-linux-gnu/libpcl_segmentation.so  
[7.374s] -- looking for PCL_VISUALIZATION
[7.376s] -- Found PCL_VISUALIZATION: /usr/lib/aarch64-linux-gnu/libpcl_visualization.so  
[7.376s] -- looking for PCL_SURFACE
[7.378s] -- Found PCL_SURFACE: /usr/lib/aarch64-linux-gnu/libpcl_surface.so  
[7.379s] -- looking for PCL_REGISTRATION
[7.381s] -- Found PCL_REGISTRATION: /usr/lib/aarch64-linux-gnu/libpcl_registration.so  
[7.382s] -- looking for PCL_KEYPOINTS
[7.384s] -- Found PCL_KEYPOINTS: /usr/lib/aarch64-linux-gnu/libpcl_keypoints.so  
[7.385s] -- looking for PCL_TRACKING
[7.387s] -- Found PCL_TRACKING: /usr/lib/aarch64-linux-gnu/libpcl_tracking.so  
[7.388s] -- looking for PCL_RECOGNITION
[7.390s] -- Found PCL_RECOGNITION: /usr/lib/aarch64-linux-gnu/libpcl_recognition.so  
[7.391s] -- looking for PCL_STEREO
[7.393s] -- Found PCL_STEREO: /usr/lib/aarch64-linux-gnu/libpcl_stereo.so  
[7.394s] -- looking for PCL_APPS
[7.396s] -- Found PCL_APPS: /usr/lib/aarch64-linux-gnu/libpcl_apps.so  
[7.398s] -- looking for PCL_IN_HAND_SCANNER
[7.399s] -- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
[7.399s] -- looking for PCL_MODELER
[7.400s] -- Found PCL_MODELER: /usr/include/pcl-1.12  
[7.401s] -- looking for PCL_POINT_CLOUD_EDITOR
[7.402s] -- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
[7.402s] -- looking for PCL_OUTOFCORE
[7.404s] -- Found PCL_OUTOFCORE: /usr/lib/aarch64-linux-gnu/libpcl_outofcore.so  
[7.405s] -- looking for PCL_PEOPLE
[7.407s] -- Found PCL_PEOPLE: /usr/lib/aarch64-linux-gnu/libpcl_people.so  
[7.410s] -- Found PCL: pcl_common;pcl_kdtree;pcl_octree;pcl_search;pcl_sample_consensus;pcl_filters;pcl_io;pcl_features;pcl_ml;pcl_segmentation;pcl_visualization;pcl_surface;pcl_registration;pcl_keypoints;pcl_tracking;pcl_recognition;pcl_stereo;pcl_apps;pcl_outofcore;pcl_people;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;/usr/lib/libOpenNI.so;libusb::libusb;/usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::GUISupportQt;FLANN::FLANN;QHULL::QHULL (Required is at least version "1.10") 
[7.416s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[7.524s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[7.576s] -- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[7.642s] -- Found std_srvs: 4.8.0 (/opt/ros/humble/share/std_srvs/cmake)
[8.124s] -- Found OpenMP_C: -fopenmp (found version "4.5") 
[8.299s] -- Found OpenMP_CXX: -fopenmp (found version "4.5") 
[8.299s] -- Found OpenMP: TRUE (found version "4.5")  
[8.306s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[8.421s] -- Found octomap_msgs: 2.0.1 (/opt/ros/humble/share/octomap_msgs/cmake)
[8.468s] -- Found tf2: 0.25.13 (/opt/ros/humble/share/tf2/cmake)
[8.480s] -- Found tf2_ros: 0.25.13 (/opt/ros/humble/share/tf2_ros/cmake)
[8.659s] -- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[8.693s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[8.697s] -- Found Eigen3: TRUE (found version "3.4.0") 
[8.698s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[8.890s] -- Configuring done (8.9s)
[9.016s] -- Generating done (0.1s)
[9.034s] -- Build files have been written to: /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[9.095s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[9.102s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[9.154s] [ 16%] [32mBuilding CXX object CMakeFiles/octomap_server.dir/rclcpp_components/node_main_octomap_server.cpp.o[0m
[9.156s] [ 50%] [32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/conversions.cpp.o[0m
[9.156s] [ 50%] [32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.o[0m
[9.158s] [ 66%] [32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/transforms.cpp.o[0m
[9.647s] In file included from [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/conversions.h:50[m[K,
[9.648s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/src/conversions.cpp:40[m[K:
[9.648s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[9.648s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[9.648s]       |  [01;35m[K^~~~~~~[m[K
[13.560s] In file included from [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/conversions.h:50[m[K,
[13.560s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:51[m[K,
[13.561s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2[m[K:
[13.561s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[13.561s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[13.561s]       |  [01;35m[K^~~~~~~[m[K
[16.163s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/conversions.cpp:[m[K In function ‘[01m[Kvoid octomap::pointsOctomapToPointCloud2(const point3d_list&, sensor_msgs::msg::PointCloud2&)[m[K’:
[16.163s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/conversions.cpp:53:57:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kpoints[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[16.163s]    53 |     void pointsOctomapToPointCloud2([01;35m[Kconst point3d_list& points[m[K,
[16.164s]       |                                     [01;35m[K~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[17.043s] [ 83%] [32m[1mLinking CXX executable octomap_server[0m
[17.296s] [ 83%] Built target octomap_server
[25.466s] In file included from [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2[m[K:
[25.467s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:[m[K In member function ‘[01m[Kvirtual void octomap_server::OctomapServer::handleNode(const iterator&)[m[K’:
[25.467s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:210:58:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kit[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.467s]   210 |         virtual void handleNode([01;35m[Kconst OcTreeT::iterator& it[m[K) {};
[25.468s]       |                                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~[m[K
[25.468s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:[m[K In member function ‘[01m[Kvirtual void octomap_server::OctomapServer::handleNodeInBBX(const iterator&)[m[K’:
[25.468s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:211:63:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kit[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.468s]   211 |         virtual void handleNodeInBBX([01;35m[Kconst OcTreeT::iterator& it[m[K) {};
[25.469s]       |                                      [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~[m[K
[25.469s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:[m[K In constructor ‘[01m[Koctomap_server::OctomapServer::OctomapServer(const rclcpp::NodeOptions&, std::string)[m[K’:
[25.469s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:148:14:[m[K [01;35m[Kwarning: [m[K‘[01m[Koctomap_server::OctomapServer::m_useColoredMap[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[25.469s]   148 |         bool [01;35m[Km_useColoredMap[m[K;
[25.469s]       |              [01;35m[K^~~~~~~~~~~~~~~[m[K
[25.470s] [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:118:16:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kdouble octomap_server::OctomapServer::m_colorFactor[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[25.470s]   118 |         double [01;35m[Km_colorFactor[m[K;
[25.470s]       |                [01;35m[K^~~~~~~~~~~~~[m[K
[25.471s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:5:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[25.471s]     5 |     [01;35m[KOctomapServer[m[K::OctomapServer(
[25.471s]       |     [01;35m[K^~~~~~~~~~~~~[m[K
[25.718s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
[25.718s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
[25.719s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
[25.719s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
[25.719s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
[25.719s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
[25.720s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
[25.720s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
[25.720s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
[25.720s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
[25.720s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[25.721s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8[m[K,
[25.721s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2[m[K:
[25.722s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kvirtual void octomap_server::OctomapServer::insertCloudCallback(const ConstSharedPtr&)[m[K’:
[25.722s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:50:[m[K [01;35m[Kwarning: [m[Kformat ‘[01m[K%s[m[K’ expects argument of type ‘[01m[Kchar*[m[K’, but argument 5 has type ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kstd::__cxx11::basic_string<char>[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=-Wformat=]8;;[m[K]
[25.722s]   341 |                 RCLCPP_ERROR(this->get_logger(), [01;35m[K"%s %"[m[K, msg, ex.what());
[25.722s]       |                                                  [01;35m[K^~~~~~[m[K
[25.723s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:52:[m[K [01;36m[Knote: [m[Kformat string is defined here
[25.723s]   341 |                 RCLCPP_ERROR(this->get_logger(), "[01;36m[K%s[m[K %", msg, ex.what());
[25.723s]       |                                                   [01;36m[K~^[m[K
[25.723s]       |                                                    [01;36m[K|[m[K
[25.723s]       |                                                    [01;36m[Kchar*[m[K
[25.724s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
[25.724s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
[25.724s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
[25.724s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
[25.724s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
[25.725s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
[25.725s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
[25.725s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
[25.725s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
[25.726s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
[25.726s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[25.726s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8[m[K,
[25.726s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2[m[K:
[25.726s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:50:[m[K [01;35m[Kwarning: [m[Kspurious trailing ‘[01m[K%[m[K’ in format [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=-Wformat=]8;;[m[K]
[25.727s]   341 |                 RCLCPP_ERROR(this->get_logger(), [01;35m[K"%s %"[m[K, msg, ex.what());
[25.727s]       |                                                  [01;35m[K^~~~~~[m[K
[25.727s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:54:[m[K [01;36m[Knote: [m[Kformat string is defined here
[25.727s]   341 |                 RCLCPP_ERROR(this->get_logger(), "%s [01;36m[K%[m[K", msg, ex.what());
[25.727s]       |                                                      [01;36m[K^[m[K
[25.728s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
[25.728s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
[25.728s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
[25.728s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
[25.728s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
[25.729s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
[25.729s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
[25.729s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
[25.731s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
[25.731s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
[25.731s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[25.732s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8[m[K,
[25.732s]                  from [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2[m[K:
[25.732s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:50:[m[K [01;35m[Kwarning: [m[Ktoo many arguments for format [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat-extra-args-Wformat-extra-args]8;;[m[K]
[25.732s]   341 |                 RCLCPP_ERROR(this->get_logger(), [01;35m[K"%s %"[m[K, msg, ex.what());
[25.733s]       |                                                  [01;35m[K^~~~~~[m[K
[25.750s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kvirtual void octomap_server::OctomapServer::publishAll(const rclcpp::Time&)[m[K’:
[25.751s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:595:28:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Ksize[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[25.751s]   595 |                     double [01;35m[Ksize[m[K = it.getSize();
[25.751s]       |                            [01;35m[K^~~~[m[K
[25.766s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kvirtual bool octomap_server::OctomapServer::octomapBinarySrv(std::shared_ptr<octomap_msgs::srv::GetOctomap_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::GetOctomap_Response_<std::allocator<void> > >)[m[K’:
[25.767s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:783:52:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kreq[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.767s]   783 |         [01;35m[Kconst std::shared_ptr<OctomapSrv::Request> req[m[K,
[25.767s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~[m[K
[25.768s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kvirtual bool octomap_server::OctomapServer::octomapFullSrv(std::shared_ptr<octomap_msgs::srv::GetOctomap_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::GetOctomap_Response_<std::allocator<void> > >)[m[K’:
[25.768s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:802:52:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kreq[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.768s]   802 |         [01;35m[Kconst std::shared_ptr<OctomapSrv::Request> req[m[K,
[25.768s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~[m[K
[25.778s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kbool octomap_server::OctomapServer::clearBBXSrv(std::shared_ptr<octomap_msgs::srv::BoundingBoxQuery_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::BoundingBoxQuery_Response_<std::allocator<void> > >)[m[K’:
[25.778s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:817:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kresp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.779s]   817 |         [01;35m[Kstd::shared_ptr<BBXSrv::Response> resp[m[K) {
[25.779s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~[m[K
[25.790s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kbool octomap_server::OctomapServer::resetSrv(std::shared_ptr<std_srvs::srv::Empty_Request_<std::allocator<void> > >, std::shared_ptr<std_srvs::srv::Empty_Response_<std::allocator<void> > >)[m[K’:
[25.790s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:855:28:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<visualization_msgs::msg::Marker_<std::allocator<void> >, std::allocator<visualization_msgs::msg::Marker_<std::allocator<void> > > >::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[25.790s]   855 |         for (auto i = 0; [01;35m[Ki < occupiedNodesVis.markers.size()[m[K; ++i){
[25.791s]       |                          [01;35m[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[25.791s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:871:28:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<visualization_msgs::msg::Marker_<std::allocator<void> >, std::allocator<visualization_msgs::msg::Marker_<std::allocator<void> > > >::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[25.791s]   871 |         for (auto i = 0; [01;35m[Ki < freeNodesVis.markers.size()[m[K; ++i) {
[25.792s]       |                          [01;35m[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[25.792s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:835:62:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kreq[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.792s]   835 |         [01;35m[Kconst std::shared_ptr<std_srvs::srv::Empty::Request> req[m[K,
[25.792s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~[m[K
[25.792s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:836:57:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kresp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.793s]   836 |         [01;35m[Kstd::shared_ptr<std_srvs::srv::Empty::Response> resp[m[K) {
[25.793s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~[m[K
[25.854s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kvirtual void octomap_server::OctomapServer::handlePreNodeTraversal(const rclcpp::Time&)[m[K’:
[25.854s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:1163:59:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kunsigned int[m[K’ and ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[25.855s]  1163 |                 for (unsigned int j = mapUpdateBBXMinY; [01;35m[Kj <= mapUpdateBBXMaxY[m[K; ++j) {
[25.855s]       |                                                         [01;35m[K~~^~~~~~~~~~~~~~~~~~~[m[K
[25.857s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:[m[K In member function ‘[01m[Kvirtual void octomap_server::OctomapServer::handlePostNodeTraversal(const rclcpp::Time&)[m[K’:
[25.857s] [01m[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:1173:29:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Krostime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[25.857s]  1173 |         [01;35m[Kconst rclcpp::Time& rostime[m[K){
[25.857s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[61.313s] [100%] [32m[1mLinking CXX shared library liboctomap_server2.so[0m
[66.769s] [100%] Built target octomap_server2
[66.802s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[66.832s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[66.842s] -- Install configuration: ""
[66.843s] -- Execute custom install script
[66.845s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server
[66.850s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so
[66.857s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py
[66.862s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2
[66.867s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2
[66.872s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh
[66.877s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv
[66.882s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh
[66.886s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv
[66.891s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash
[66.896s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh
[66.901s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh
[66.906s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv
[66.911s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv
[66.919s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2
[66.927s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2
[66.934s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake
[66.942s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake
[66.949s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml
[66.964s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
