#pragma once
#include "imu_processor.h"
#include "lidar_processor.h"
#include "grid_map_processor.h"
#include <Eigen/Dense>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

enum BuilderStatus
{
    IMU_INIT,
    MAP_INIT,
    MAPPING
};

class MapBuilder
{
public:
    MapBuilder(Config &config, std::shared_ptr<IESKF> kf);

    void process(SyncPackage &package);
    BuilderStatus status() { return m_status; }    
    std::shared_ptr<LidarProcessor> lidar_processor(){return m_lidar_processor;}
    std::shared_ptr<fastlio::GridMapProcessor> point_cloud_processor() { return m_grid_map_processor; }

private:
    Config m_config;
    BuilderStatus m_status;
    std::shared_ptr<IESKF> m_kf;
    std::shared_ptr<IMUProcessor> m_imu_processor;
    std::shared_ptr<LidarProcessor> m_lidar_processor;
    std::shared_ptr<fastlio::GridMapProcessor> m_grid_map_processor;  // 重命名为point_cloud_processor更合适，但保持兼容性
};