{"artifacts": [{"path": "liboctomap_server2.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "find_VTK", "find_external_library", "set_property", "_populate_OpenGL_target_properties", "find_flann", "find_qhull", "add_compile_options", "target_compile_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgsConfig.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/export_tf2_geometry_msgsExport.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/tf2_geometry_msgsConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/export_rclcpp_componentsExport.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/rclcpp_componentsConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Qt5/Qt5Config.cmake", "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake", "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindFLANN.cmake", "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindQhull.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Qhull/QhullTargets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Qhull/QhullConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 43, "parent": 0}, {"command": 2, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 1, "line": 145, "parent": 2}, {"command": 1, "file": 1, "line": 151, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 0}, {"file": 4, "parent": 5}, {"command": 4, "file": 4, "line": 41, "parent": 6}, {"file": 3, "parent": 7}, {"command": 4, "file": 3, "line": 9, "parent": 8}, {"file": 2, "parent": 9}, {"command": 3, "file": 2, "line": 56, "parent": 10}, {"command": 4, "file": 4, "line": 41, "parent": 6}, {"file": 8, "parent": 12}, {"command": 5, "file": 8, "line": 21, "parent": 13}, {"file": 7, "parent": 14}, {"command": 4, "file": 7, "line": 41, "parent": 15}, {"file": 6, "parent": 16}, {"command": 4, "file": 6, "line": 9, "parent": 17}, {"file": 5, "parent": 18}, {"command": 3, "file": 5, "line": 56, "parent": 19}, {"command": 5, "file": 8, "line": 21, "parent": 13}, {"file": 11, "parent": 21}, {"command": 4, "file": 11, "line": 41, "parent": 22}, {"file": 10, "parent": 23}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 9, "parent": 25}, {"command": 3, "file": 9, "line": 56, "parent": 26}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 12, "parent": 28}, {"command": 3, "file": 12, "line": 56, "parent": 29}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 13, "parent": 31}, {"command": 3, "file": 13, "line": 56, "parent": 32}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 14, "parent": 34}, {"command": 3, "file": 14, "line": 56, "parent": 35}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 15, "parent": 37}, {"command": 3, "file": 15, "line": 56, "parent": 38}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 16, "parent": 40}, {"command": 3, "file": 16, "line": 56, "parent": 41}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 17, "parent": 43}, {"command": 3, "file": 17, "line": 56, "parent": 44}, {"command": 4, "file": 10, "line": 9, "parent": 24}, {"file": 18, "parent": 46}, {"command": 3, "file": 18, "line": 56, "parent": 47}, {"command": 5, "file": 0, "line": 36, "parent": 0}, {"file": 21, "parent": 49}, {"command": 4, "file": 21, "line": 41, "parent": 50}, {"file": 20, "parent": 51}, {"command": 4, "file": 20, "line": 9, "parent": 52}, {"file": 19, "parent": 53}, {"command": 3, "file": 19, "line": 56, "parent": 54}, {"command": 5, "file": 0, "line": 19, "parent": 0}, {"file": 28, "parent": 56}, {"command": 4, "file": 28, "line": 41, "parent": 57}, {"file": 27, "parent": 58}, {"command": 5, "file": 27, "line": 21, "parent": 59}, {"file": 26, "parent": 60}, {"command": 4, "file": 26, "line": 41, "parent": 61}, {"file": 25, "parent": 62}, {"command": 5, "file": 25, "line": 21, "parent": 63}, {"file": 24, "parent": 64}, {"command": 4, "file": 24, "line": 41, "parent": 65}, {"file": 23, "parent": 66}, {"command": 4, "file": 23, "line": 9, "parent": 67}, {"file": 22, "parent": 68}, {"command": 3, "file": 22, "line": 56, "parent": 69}, {"command": 4, "file": 24, "line": 41, "parent": 65}, {"file": 32, "parent": 71}, {"command": 5, "file": 32, "line": 21, "parent": 72}, {"file": 31, "parent": 73}, {"command": 4, "file": 31, "line": 41, "parent": 74}, {"file": 30, "parent": 75}, {"command": 4, "file": 30, "line": 9, "parent": 76}, {"file": 29, "parent": 77}, {"command": 3, "file": 29, "line": 56, "parent": 78}, {"command": 5, "file": 0, "line": 20, "parent": 0}, {"file": 35, "parent": 80}, {"command": 4, "file": 35, "line": 41, "parent": 81}, {"file": 34, "parent": 82}, {"command": 4, "file": 34, "line": 9, "parent": 83}, {"file": 33, "parent": 84}, {"command": 3, "file": 33, "line": 56, "parent": 85}, {"command": 4, "file": 35, "line": 41, "parent": 81}, {"file": 39, "parent": 87}, {"command": 5, "file": 39, "line": 21, "parent": 88}, {"file": 38, "parent": 89}, {"command": 4, "file": 38, "line": 41, "parent": 90}, {"file": 37, "parent": 91}, {"command": 4, "file": 37, "line": 9, "parent": 92}, {"file": 36, "parent": 93}, {"command": 3, "file": 36, "line": 56, "parent": 94}, {"command": 3, "file": 33, "line": 64, "parent": 85}, {"command": 5, "file": 0, "line": 21, "parent": 0}, {"file": 40, "parent": 97}, {"command": 1, "file": 40, "line": 704, "parent": 98}, {"command": 7, "file": 40, "line": 540, "parent": 98}, {"command": 6, "file": 40, "line": 319, "parent": 100}, {"command": 5, "file": 40, "line": 270, "parent": 101}, {"file": 42, "parent": 102}, {"command": 4, "file": 42, "line": 138, "parent": 103}, {"file": 41, "parent": 104}, {"command": 3, "file": 41, "line": 940, "parent": 105}, {"command": 3, "file": 41, "line": 326, "parent": 105}, {"command": 3, "file": 41, "line": 308, "parent": 105}, {"command": 3, "file": 41, "line": 148, "parent": 105}, {"command": 3, "file": 41, "line": 301, "parent": 105}, {"command": 3, "file": 41, "line": 615, "parent": 105}, {"command": 4, "file": 42, "line": 150, "parent": 103}, {"file": 45, "parent": 112}, {"command": 5, "file": 45, "line": 1008, "parent": 113}, {"file": 44, "parent": 114}, {"command": 5, "file": 44, "line": 28, "parent": 115}, {"file": 43, "parent": 116}, {"command": 9, "file": 43, "line": 213, "parent": 117}, {"command": 8, "file": 43, "line": 57, "parent": 118}, {"command": 3, "file": 41, "line": 123, "parent": 105}, {"command": 3, "file": 41, "line": 639, "parent": 105}, {"command": 7, "file": 40, "line": 543, "parent": 98}, {"command": 10, "file": 40, "line": 301, "parent": 122}, {"command": 5, "file": 40, "line": 256, "parent": 123}, {"file": 46, "parent": 124}, {"command": 8, "file": 46, "line": 217, "parent": 125}, {"command": 7, "file": 40, "line": 540, "parent": 98}, {"command": 11, "file": 40, "line": 303, "parent": 127}, {"command": 5, "file": 40, "line": 158, "parent": 128}, {"file": 47, "parent": 129}, {"command": 8, "file": 47, "line": 66, "parent": 130}, {"command": 5, "file": 47, "line": 45, "parent": 130}, {"file": 49, "parent": 132}, {"command": 4, "file": 49, "line": 1, "parent": 133}, {"file": 48, "parent": 134}, {"command": 3, "file": 48, "line": 74, "parent": 135}, {"command": 12, "file": 0, "line": 15, "parent": 0}, {"command": 13, "file": 1, "line": 128, "parent": 2}, {"command": 14, "file": 0, "line": 41, "parent": 0}, {"command": 15, "file": 1, "line": 147, "parent": 2}, {"command": 14, "file": 0, "line": 40, "parent": 0}, {"command": 15, "file": 1, "line": 141, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17 -fPIC"}, {"backtrace": 137, "fragment": "-Wall"}, {"backtrace": 137, "fragment": "-Wextra"}, {"backtrace": 137, "fragment": "-Wpedantic"}, {"backtrace": 4, "fragment": "-fPIC"}], "defines": [{"backtrace": 4, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 4, "define": "BOOST_DATE_TIME_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_IOSTREAMS_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SERIALIZATION_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 3, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 138, "define": "DISABLE_PCAP"}, {"backtrace": 4, "define": "QT_CORE_LIB"}, {"backtrace": 4, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_NO_DEBUG"}, {"backtrace": 4, "define": "QT_OPENGL_LIB"}, {"backtrace": 4, "define": "QT_WIDGETS_LIB"}, {"backtrace": 3, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 4, "define": "kiss_fft_scalar=double"}, {"define": "octomap_server2_EXPORTS"}], "includes": [{"backtrace": 139, "path": "/home/<USER>/fastlio2/octomap_ros2/include"}, {"backtrace": 140, "path": "/opt/ros/humble/include/pcl_conversions"}, {"backtrace": 140, "path": "/opt/ros/humble/include"}, {"backtrace": 141, "isSystem": true, "path": "/usr/include/pcl-1.12"}, {"backtrace": 141, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 141, "isSystem": true, "path": "/usr/include/ni"}, {"backtrace": 141, "isSystem": true, "path": "/usr/include/openni2"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/nav_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/std_srvs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 142, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_components"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/humble/include/pcl_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/composition_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/vtk-9.1"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/jsoncpp"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/freetype2"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtOpenGL"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtWidgets"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtGui"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtCore"}, {"backtrace": 4, "isSystem": true, "path": "/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++"}], "language": "CXX", "languageStandard": {"backtraces": [3, 4, 4, 4], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "id": "octomap_server2::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:/opt/ros/humble/lib/aarch64-linux-gnu", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_common.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_apps.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_outofcore.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_people.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libflann_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/usr/lib/aarch64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 79, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 86, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_surface.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_keypoints.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_tracking.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_recognition.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_registration.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_stereo.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_segmentation.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_features.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_filters.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_sample_consensus.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_ml.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_visualization.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_search.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_kdtree.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_io.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_octree.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/usr/lib/aarch64-linux-gnu/libpng.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/usr/lib/aarch64-linux-gnu/libz.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkChartsCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkInteractionImage-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkIOGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 106, "fragment": "/usr/lib/aarch64-linux-gnu/libjsoncpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkIOPLY-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingLOD-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkViewsContext2D-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkViewsCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkGUISupportQt-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkInteractionWidgets-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkFiltersModeling-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkInteractionStyle-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkFiltersExtraction-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkIOLegacy-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkIOCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingAnnotation-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingContext2D-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingFreeType-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 107, "fragment": "/usr/lib/aarch64-linux-gnu/libfreetype.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkImagingSources-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkIOImage-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkImagingCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingOpenGL2-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 108, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingUI-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkRenderingCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonColor-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkFiltersGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkFiltersSources-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkFiltersGeneral-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonComputationalGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkFiltersCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonExecutionModel-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonDataModel-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonMisc-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonTransforms-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonMath-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 109, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkkissfft-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 110, "fragment": "/usr/lib/aarch64-linux-gnu/libGLEW.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/usr/lib/aarch64-linux-gnu/libX11.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5OpenGL.so.5.15.3", "role": "libraries"}, {"backtrace": 111, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5Widgets.so.5.15.3", "role": "libraries"}, {"backtrace": 119, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5Gui.so.5.15.3", "role": "libraries"}, {"backtrace": 119, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5Core.so.5.15.3", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libvtkCommonCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 120, "fragment": "/usr/lib/aarch64-linux-gnu/libtbb.so.12.5", "role": "libraries"}, {"backtrace": 121, "fragment": "/usr/lib/aarch64-linux-gnu/libvtksys-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libpcl_common.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_system.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_iostreams.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_serialization.so.1.74.0", "role": "libraries"}, {"backtrace": 126, "fragment": "-llz4", "role": "libraries"}, {"backtrace": 131, "fragment": "/usr/lib/aarch64-linux-gnu/libqhull_r.so.8.0.2", "role": "libraries"}, {"backtrace": 136, "fragment": "-lm", "role": "libraries"}], "language": "CXX"}, "name": "octomap_server2", "nameOnDisk": "liboctomap_server2.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/octomap_server.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/transforms.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/conversions.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}