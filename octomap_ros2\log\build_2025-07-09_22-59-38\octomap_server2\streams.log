[0.029s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[0.090s] [ 33%] Built target octomap_server
[0.134s] [100%] Built target octomap_server2
[0.144s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[0.155s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[0.160s] -- Install configuration: ""
[0.162s] -- Execute custom install script
[0.162s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server
[0.163s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so
[0.164s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py
[0.164s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2
[0.165s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2
[0.165s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh
[0.165s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv
[0.166s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh
[0.166s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv
[0.167s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash
[0.167s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh
[0.168s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh
[0.168s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv
[0.169s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv
[0.175s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2
[0.175s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2
[0.176s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake
[0.176s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake
[0.176s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml
[0.186s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
