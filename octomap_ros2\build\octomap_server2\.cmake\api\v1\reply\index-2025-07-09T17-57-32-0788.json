{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/local/bin/cmake", "cpack": "/usr/local/bin/cpack", "ctest": "/usr/local/bin/ctest", "root": "/usr/local/share/cmake-3.27"}, "version": {"isDirty": false, "major": 3, "minor": 27, "patch": 9, "string": "3.27.9", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-61682c1f3bc919b5d576.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-61682c1f3bc919b5d576.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}}}}