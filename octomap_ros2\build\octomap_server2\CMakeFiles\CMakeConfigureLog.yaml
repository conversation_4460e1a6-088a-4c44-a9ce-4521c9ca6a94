
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 5.15.136-tegra - aarch64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/3.27.9/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/3.27.9/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_db940/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_db940.dir/build.make CMakeFiles/cmTC_db940.dir/build
        gmake[1]: Entering directory '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM'
        Building C object CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake-3.27/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_db940.dir/'
         /usr/lib/gcc/aarch64-linux-gnu/11/cc1 -quiet -v -imultiarch aarch64-linux-gnu /usr/local/share/cmake-3.27/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_db940.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mlittle-endian -mabi=lp64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -o /tmp/ccrvTiTY.s
        GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/local/include/aarch64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/../../../../aarch64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-linux-gnu/11/include
         /usr/local/include
         /usr/include/aarch64-linux-gnu
         /usr/include
        End of search list.
        GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 52ed857e9cd110e5efaa797811afcfbb
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_db940.dir/'
         as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o /tmp/ccrvTiTY.s
        GNU assembler version 2.38 (aarch64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.'
        Linking C executable cmTC_db940
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_db940.dir/link.txt --verbose=1
        /usr/bin/cc  -v CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o -o cmTC_db940 
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_db940' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_db940.'
         /usr/lib/gcc/aarch64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccBzp8xa.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu --as-needed -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -z now -z relro -o cmTC_db940 /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/11 -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/11/../../.. CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_db940' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_db940.'
        gmake[1]: Leaving directory '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-linux-gnu/11/include]
          add: [/usr/local/include]
          add: [/usr/include/aarch64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/11/include] ==> [/usr/lib/gcc/aarch64-linux-gnu/11/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/aarch64-linux-gnu] ==> [/usr/include/aarch64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/gcc/aarch64-linux-gnu/11/include;/usr/local/include;/usr/include/aarch64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_db940/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_db940.dir/build.make CMakeFiles/cmTC_db940.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-X4dqqM']
        ignore line: [Building C object CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake-3.27/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_db940.dir/']
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/11/cc1 -quiet -v -imultiarch aarch64-linux-gnu /usr/local/share/cmake-3.27/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_db940.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mlittle-endian -mabi=lp64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -o /tmp/ccrvTiTY.s]
        ignore line: [GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/local/include/aarch64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/../../../../aarch64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/11/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/aarch64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 52ed857e9cd110e5efaa797811afcfbb]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_db940.dir/']
        ignore line: [ as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o /tmp/ccrvTiTY.s]
        ignore line: [GNU assembler version 2.38 (aarch64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_db940]
        ignore line: [/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_db940.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/cc  -v CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o -o cmTC_db940 ]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_db940' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_db940.']
        link line: [ /usr/lib/gcc/aarch64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccBzp8xa.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu --as-needed -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -z now -z relro -o cmTC_db940 /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/11 -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/11/../../.. CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccBzp8xa.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [--fix-cortex-a53-843419] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_db940] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib]
          arg [-L/lib/aarch64-linux-gnu] ==> dir [/lib/aarch64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/aarch64-linux-gnu] ==> dir [/usr/lib/aarch64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11/../../..] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../..]
          arg [CMakeFiles/cmTC_db940.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o] ==> [/usr/lib/aarch64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o] ==> [/usr/lib/aarch64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o] ==> [/usr/lib/aarch64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11] ==> [/usr/lib/gcc/aarch64-linux-gnu/11]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/aarch64-linux-gnu] ==> [/lib/aarch64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib/aarch64-linux-gnu/Scrt1.o;/usr/lib/aarch64-linux-gnu/crti.o;/usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o;/usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o;/usr/lib/aarch64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-linux-gnu/11;/usr/lib/aarch64-linux-gnu;/usr/lib;/lib/aarch64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_06693/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_06693.dir/build.make CMakeFiles/cmTC_06693.dir/build
        gmake[1]: Entering directory '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw'
        Building CXX object CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o -c /usr/local/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_06693.dir/'
         /usr/lib/gcc/aarch64-linux-gnu/11/cc1plus -quiet -v -imultiarch aarch64-linux-gnu -D_GNU_SOURCE /usr/local/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_06693.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mlittle-endian -mabi=lp64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -o /tmp/ccgMdlfb.s
        GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/include/aarch64-linux-gnu/c++/11"
        ignoring nonexistent directory "/usr/local/include/aarch64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/../../../../aarch64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/include/c++/11
         /usr/include/aarch64-linux-gnu/c++/11
         /usr/include/c++/11/backward
         /usr/lib/gcc/aarch64-linux-gnu/11/include
         /usr/local/include
         /usr/include/aarch64-linux-gnu
         /usr/include
        End of search list.
        GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 3e6e780af1232722b47e0979fda82402
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_06693.dir/'
         as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccgMdlfb.s
        GNU assembler version 2.38 (aarch64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_06693
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_06693.dir/link.txt --verbose=1
        /usr/bin/c++  -v CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_06693 
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_06693' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_06693.'
         /usr/lib/gcc/aarch64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccNOITI0.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu --as-needed -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -z now -z relro -o cmTC_06693 /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/11 -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/11/../../.. CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_06693' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_06693.'
        gmake[1]: Leaving directory '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/include/c++/11]
          add: [/usr/include/aarch64-linux-gnu/c++/11]
          add: [/usr/include/c++/11/backward]
          add: [/usr/lib/gcc/aarch64-linux-gnu/11/include]
          add: [/usr/local/include]
          add: [/usr/include/aarch64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/include/c++/11] ==> [/usr/include/c++/11]
        collapse include dir [/usr/include/aarch64-linux-gnu/c++/11] ==> [/usr/include/aarch64-linux-gnu/c++/11]
        collapse include dir [/usr/include/c++/11/backward] ==> [/usr/include/c++/11/backward]
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/11/include] ==> [/usr/lib/gcc/aarch64-linux-gnu/11/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/aarch64-linux-gnu] ==> [/usr/include/aarch64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/11;/usr/include/aarch64-linux-gnu/c++/11;/usr/include/c++/11/backward;/usr/lib/gcc/aarch64-linux-gnu/11/include;/usr/local/include;/usr/include/aarch64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/usr/local/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_06693/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_06693.dir/build.make CMakeFiles/cmTC_06693.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-XFqnqw']
        ignore line: [Building CXX object CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o -c /usr/local/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_06693.dir/']
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/11/cc1plus -quiet -v -imultiarch aarch64-linux-gnu -D_GNU_SOURCE /usr/local/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_06693.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mlittle-endian -mabi=lp64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -o /tmp/ccgMdlfb.s]
        ignore line: [GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/include/aarch64-linux-gnu/c++/11"]
        ignore line: [ignoring nonexistent directory "/usr/local/include/aarch64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/../../../../aarch64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/include/c++/11]
        ignore line: [ /usr/include/aarch64-linux-gnu/c++/11]
        ignore line: [ /usr/include/c++/11/backward]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/11/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/aarch64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 3e6e780af1232722b47e0979fda82402]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_06693.dir/']
        ignore line: [ as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccgMdlfb.s]
        ignore line: [GNU assembler version 2.38 (aarch64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_06693]
        ignore line: [/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_06693.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/c++  -v CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_06693 ]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_06693' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_06693.']
        link line: [ /usr/lib/gcc/aarch64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccNOITI0.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu --as-needed -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -z now -z relro -o cmTC_06693 /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/11 -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/11/../../.. CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccNOITI0.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [--fix-cortex-a53-843419] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_06693] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib]
          arg [-L/lib/aarch64-linux-gnu] ==> dir [/lib/aarch64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/aarch64-linux-gnu] ==> dir [/usr/lib/aarch64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/11/../../..] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../..]
          arg [CMakeFiles/cmTC_06693.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o] ==> [/usr/lib/aarch64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o] ==> [/usr/lib/aarch64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o] ==> [/usr/lib/aarch64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11] ==> [/usr/lib/gcc/aarch64-linux-gnu/11]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/aarch64-linux-gnu] ==> [/lib/aarch64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/11/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/aarch64-linux-gnu/Scrt1.o;/usr/lib/aarch64-linux-gnu/crti.o;/usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o;/usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o;/usr/lib/aarch64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-linux-gnu/11;/usr/lib/aarch64-linux-gnu;/usr/lib;/lib/aarch64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake-3.27/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "/usr/local/share/cmake-3.27/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "/usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfig.cmake:40 (find_package)"
      - "/opt/ros/humble/share/rcl_logging_spdlog/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/rcl_logging_spdlog/cmake/rcl_logging_spdlogConfig.cmake:41 (include)"
      - "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake:41 (include)"
      - "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake:41 (include)"
      - "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake:41 (include)"
      - "CMakeLists.txt:19 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-yolCNd"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-yolCNd"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-yolCNd'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_5a92c/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_5a92c.dir/build.make CMakeFiles/cmTC_5a92c.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-yolCNd”
        Building C object CMakeFiles/cmTC_5a92c.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_5a92c.dir/src.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-yolCNd/src.c
        Linking C executable cmTC_5a92c
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5a92c.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_5a92c.dir/src.c.o -o cmTC_5a92c 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-yolCNd”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/FindMPI.cmake:1278 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindMPI.cmake:1322 (_MPI_try_staged_settings)"
      - "/usr/local/share/cmake-3.27/Modules/FindMPI.cmake:1645 (_MPI_check_lang_works)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:397 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    description: "The MPI test test_mpi for C in mode normal"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-CYh4wd"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-CYh4wd"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "MPI_RESULT_C_test_mpi_normal"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-CYh4wd'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_d1266/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_d1266.dir/build.make CMakeFiles/cmTC_d1266.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-CYh4wd”
        Building C object CMakeFiles/cmTC_d1266.dir/test_mpi.c.o
        /usr/bin/cc  -isystem /usr/lib/aarch64-linux-gnu/openmpi/include -isystem /usr/lib/aarch64-linux-gnu/openmpi/include/openmpi -std=gnu99 -o CMakeFiles/cmTC_d1266.dir/test_mpi.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-CYh4wd/test_mpi.c
        Linking C executable cmTC_d1266
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d1266.dir/link.txt --verbose=1
        /usr/bin/cc -L/usr/lib/aarch64-linux-gnu/openmpi/lib CMakeFiles/cmTC_d1266.dir/test_mpi.c.o -o cmTC_d1266  /usr/lib/aarch64-linux-gnu/libmpi.so 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-CYh4wd”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindX11.cmake:682 (check_library_exists)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:1149 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-5AKfdo"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-5AKfdo"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "X11_LIB_X11_SOLO"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-5AKfdo'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_ffabf/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_ffabf.dir/build.make CMakeFiles/cmTC_ffabf.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-5AKfdo”
        Building C object CMakeFiles/cmTC_ffabf.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=XOpenDisplay -o CMakeFiles/cmTC_ffabf.dir/CheckFunctionExists.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-5AKfdo/CheckFunctionExists.c
        Linking C executable cmTC_ffabf
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ffabf.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=XOpenDisplay CMakeFiles/cmTC_ffabf.dir/CheckFunctionExists.c.o -o cmTC_ffabf  /usr/lib/aarch64-linux-gnu/libX11.so /usr/lib/aarch64-linux-gnu/libXext.so 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-5AKfdo”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindX11.cmake:697 (check_function_exists)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:1149 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Looking for gethostbyname"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-du5rlt"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-du5rlt"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "CMAKE_HAVE_GETHOSTBYNAME"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-du5rlt'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_9f7c9/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_9f7c9.dir/build.make CMakeFiles/cmTC_9f7c9.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-du5rlt”
        Building C object CMakeFiles/cmTC_9f7c9.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=gethostbyname -std=gnu99 -o CMakeFiles/cmTC_9f7c9.dir/CheckFunctionExists.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-du5rlt/CheckFunctionExists.c
        Linking C executable cmTC_9f7c9
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9f7c9.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=gethostbyname CMakeFiles/cmTC_9f7c9.dir/CheckFunctionExists.c.o -o cmTC_9f7c9 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-du5rlt”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindX11.cmake:711 (check_function_exists)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:1149 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Looking for connect"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-lVi7QS"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-lVi7QS"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "CMAKE_HAVE_CONNECT"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-lVi7QS'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_64d42/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_64d42.dir/build.make CMakeFiles/cmTC_64d42.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-lVi7QS”
        Building C object CMakeFiles/cmTC_64d42.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=connect -std=gnu99 -o CMakeFiles/cmTC_64d42.dir/CheckFunctionExists.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-lVi7QS/CheckFunctionExists.c
        Linking C executable cmTC_64d42
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_64d42.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=connect CMakeFiles/cmTC_64d42.dir/CheckFunctionExists.c.o -o cmTC_64d42 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-lVi7QS”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindX11.cmake:720 (check_function_exists)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:1149 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Looking for remove"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-GnL5Id"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-GnL5Id"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "CMAKE_HAVE_REMOVE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-GnL5Id'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_6a523/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_6a523.dir/build.make CMakeFiles/cmTC_6a523.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-GnL5Id”
        Building C object CMakeFiles/cmTC_6a523.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=remove -std=gnu99 -o CMakeFiles/cmTC_6a523.dir/CheckFunctionExists.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-GnL5Id/CheckFunctionExists.c
        Linking C executable cmTC_6a523
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6a523.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=remove CMakeFiles/cmTC_6a523.dir/CheckFunctionExists.c.o -o cmTC_6a523 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-GnL5Id”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindX11.cmake:729 (check_function_exists)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:1149 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Looking for shmat"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-Gda7bI"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-Gda7bI"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "CMAKE_HAVE_SHMAT"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-Gda7bI'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_b8e3b/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_b8e3b.dir/build.make CMakeFiles/cmTC_b8e3b.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-Gda7bI”
        Building C object CMakeFiles/cmTC_b8e3b.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=shmat -std=gnu99 -o CMakeFiles/cmTC_b8e3b.dir/CheckFunctionExists.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-Gda7bI/CheckFunctionExists.c
        Linking C executable cmTC_b8e3b
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b8e3b.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=shmat CMakeFiles/cmTC_b8e3b.dir/CheckFunctionExists.c.o -o cmTC_b8e3b 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-Gda7bI”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindX11.cmake:739 (check_library_exists)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:1149 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Looking for IceConnectionNumber in ICE"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-9l4wIT"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-9l4wIT"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "CMAKE_LIB_ICE_HAS_ICECONNECTIONNUMBER"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-9l4wIT'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_134cb/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_134cb.dir/build.make CMakeFiles/cmTC_134cb.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-9l4wIT”
        Building C object CMakeFiles/cmTC_134cb.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=IceConnectionNumber -o CMakeFiles/cmTC_134cb.dir/CheckFunctionExists.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-9l4wIT/CheckFunctionExists.c
        Linking C executable cmTC_134cb
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_134cb.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=IceConnectionNumber CMakeFiles/cmTC_134cb.dir/CheckFunctionExists.c.o -o cmTC_134cb  -lICE 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-9l4wIT”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:206 (try_compile)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:451 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:28 (find_package)"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_fopenmp"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_5f16a/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_5f16a.dir/build.make CMakeFiles/cmTC_5f16a.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        Building C object CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o
        /usr/bin/cc   -fopenmp -v -o CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/FindOpenMP/OpenMPTryFlag.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'CMakeFiles/cmTC_5f16a.dir/'
         /usr/lib/gcc/aarch64-linux-gnu/11/cc1 -quiet -v -imultiarch aarch64-linux-gnu -D_REENTRANT /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/FindOpenMP/OpenMPTryFlag.c -quiet -dumpdir CMakeFiles/cmTC_5f16a.dir/ -dumpbase OpenMPTryFlag.c.c -dumpbase-ext .c -mlittle-endian -mabi=lp64 -version -fopenmp -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -o /tmp/ccHiFJ53.s
        GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/local/include/aarch64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/../../../../aarch64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-linux-gnu/11/include
         /usr/local/include
         /usr/include/aarch64-linux-gnu
         /usr/include
        End of search list.
        GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 52ed857e9cd110e5efaa797811afcfbb
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'CMakeFiles/cmTC_5f16a.dir/'
         as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o /tmp/ccHiFJ53.s
        GNU汇编版本 2.38 (aarch64-linux-gnu) 使用BFD版本 (GNU Binutils for Ubuntu) 2.38
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.'
        Linking C executable cmTC_5f16a
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5f16a.dir/link.txt --verbose=1
        /usr/bin/cc  -fopenmp -v CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o -o cmTC_5f16a  -v 
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        Reading specs from /usr/lib/gcc/aarch64-linux-gnu/11/libgomp.spec
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_5f16a' '-v' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'cmTC_5f16a.'
         /usr/lib/gcc/aarch64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc0WQPPH.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu --as-needed -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -z now -z relro -o cmTC_5f16a /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/11 -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/11/../../.. CMakeFiles/cmTC_5f16a.dir/OpenMPTryFlag.c.o -lgomp -lgcc --push-state --as-needed -lgcc_s --pop-state -lpthread -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_5f16a' '-v' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'cmTC_5f16a.'
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:206 (try_compile)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:451 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:28 (find_package)"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_fopenmp"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_84138/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_84138.dir/build.make CMakeFiles/cmTC_84138.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        Building CXX object CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o
        /usr/bin/c++   -fopenmp -v -o CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/FindOpenMP/OpenMPTryFlag.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'CMakeFiles/cmTC_84138.dir/'
         /usr/lib/gcc/aarch64-linux-gnu/11/cc1plus -quiet -v -imultiarch aarch64-linux-gnu -D_GNU_SOURCE -D_REENTRANT /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/FindOpenMP/OpenMPTryFlag.cpp -quiet -dumpdir CMakeFiles/cmTC_84138.dir/ -dumpbase OpenMPTryFlag.cpp.cpp -dumpbase-ext .cpp -mlittle-endian -mabi=lp64 -version -fopenmp -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -o /tmp/ccWHfBjG.s
        GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/include/aarch64-linux-gnu/c++/11"
        ignoring nonexistent directory "/usr/local/include/aarch64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/11/../../../../aarch64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/include/c++/11
         /usr/include/aarch64-linux-gnu/c++/11
         /usr/include/c++/11/backward
         /usr/lib/gcc/aarch64-linux-gnu/11/include
         /usr/local/include
         /usr/include/aarch64-linux-gnu
         /usr/include
        End of search list.
        GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04) version 11.4.0 (aarch64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 3e6e780af1232722b47e0979fda82402
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'CMakeFiles/cmTC_84138.dir/'
         as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o /tmp/ccWHfBjG.s
        GNU汇编版本 2.38 (aarch64-linux-gnu) 使用BFD版本 (GNU Binutils for Ubuntu) 2.38
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.'
        Linking CXX executable cmTC_84138
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_84138.dir/link.txt --verbose=1
        /usr/bin/c++  -fopenmp -v CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o -o cmTC_84138  -v 
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper
        Target: aarch64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
        COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/11/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/11/../../../:/lib/:/usr/lib/
        Reading specs from /usr/lib/gcc/aarch64-linux-gnu/11/libgomp.spec
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_84138' '-v' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'cmTC_84138.'
         /usr/lib/gcc/aarch64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccAaLScI.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu --as-needed -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -z now -z relro -o cmTC_84138 /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/11 -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/11/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/11/../../.. CMakeFiles/cmTC_84138.dir/OpenMPTryFlag.cpp.o -lstdc++ -lm -lgomp -lgcc_s -lgcc -lpthread -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/11/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/11/../../../aarch64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-fopenmp' '-v' '-o' 'cmTC_84138' '-v' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-pthread' '-dumpdir' 'cmTC_84138.'
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:386 (try_compile)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:514 (_OPENMP_GET_SPEC_DATE)"
      - "CMakeLists.txt:28 (find_package)"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_d99c3/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_d99c3.dir/build.make CMakeFiles/cmTC_d99c3.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        Building C object CMakeFiles/cmTC_d99c3.dir/OpenMPCheckVersion.c.o
        /usr/bin/cc   -fopenmp -o CMakeFiles/cmTC_d99c3.dir/OpenMPCheckVersion.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/FindOpenMP/OpenMPCheckVersion.c
        Linking C executable cmTC_d99c3
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d99c3.dir/link.txt --verbose=1
        /usr/bin/cc  -fopenmp CMakeFiles/cmTC_d99c3.dir/OpenMPCheckVersion.c.o -o cmTC_d99c3 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:386 (try_compile)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindOpenMP.cmake:514 (_OPENMP_GET_SPEC_DATE)"
      - "CMakeLists.txt:28 (find_package)"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_aa016/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_aa016.dir/build.make CMakeFiles/cmTC_aa016.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        Building CXX object CMakeFiles/cmTC_aa016.dir/OpenMPCheckVersion.cpp.o
        /usr/bin/c++   -fopenmp -o CMakeFiles/cmTC_aa016.dir/OpenMPCheckVersion.cpp.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/FindOpenMP/OpenMPCheckVersion.cpp
        Linking CXX executable cmTC_aa016
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_aa016.dir/link.txt --verbose=1
        /usr/bin/c++  -fopenmp CMakeFiles/cmTC_aa016.dir/OpenMPCheckVersion.cpp.o -o cmTC_aa016 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeTmp”
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.27/Modules/FindMPI.cmake:1278 (try_compile)"
      - "/usr/local/share/cmake-3.27/Modules/FindMPI.cmake:1322 (_MPI_try_staged_settings)"
      - "/usr/local/share/cmake-3.27/Modules/FindMPI.cmake:1645 (_MPI_check_lang_works)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/VTK-vtk-module-find-packages.cmake:397 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/vtk-config.cmake:150 (include)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:270 (find_package)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:319 (find_VTK)"
      - "/usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:540 (find_external_library)"
      - "CMakeLists.txt:21 (find_package)"
    description: "The MPI test test_mpi for C in mode normal"
    directories:
      source: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-xPPOgl"
      binary: "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-xPPOgl"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1/patches/99;/usr/lib/aarch64-linux-gnu/cmake/vtk-9.1;/opt/ros/humble/share/fastrtps_cmake_module/cmake/Modules;/usr/lib/aarch64-linux-gnu/cmake/pcl/Modules"
    buildResult:
      variable: "MPI_RESULT_C_test_mpi_normal"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-xPPOgl'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_fe2ce/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_fe2ce.dir/build.make CMakeFiles/cmTC_fe2ce.dir/build
        gmake[1]: 进入目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-xPPOgl”
        Building C object CMakeFiles/cmTC_fe2ce.dir/test_mpi.c.o
        /usr/bin/cc  -isystem /usr/lib/aarch64-linux-gnu/openmpi/include -isystem /usr/lib/aarch64-linux-gnu/openmpi/include/openmpi -std=gnu99 -o CMakeFiles/cmTC_fe2ce.dir/test_mpi.c.o -c /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-xPPOgl/test_mpi.c
        Linking C executable cmTC_fe2ce
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fe2ce.dir/link.txt --verbose=1
        /usr/bin/cc -L/usr/lib/aarch64-linux-gnu/openmpi/lib CMakeFiles/cmTC_fe2ce.dir/test_mpi.c.o -o cmTC_fe2ce  /usr/lib/aarch64-linux-gnu/libmpi.so 
        gmake[1]: 离开目录“/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles/CMakeScratch/TryCompile-xPPOgl”
        
      exitCode: 0
...
