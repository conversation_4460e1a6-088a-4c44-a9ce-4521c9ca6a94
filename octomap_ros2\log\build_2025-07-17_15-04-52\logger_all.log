[0.237s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.237s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0xffff9a455450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff9a4548b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff9a4548b0>>)
[0.595s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.596s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.596s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.596s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.596s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.596s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.596s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/fastlio2/octomap_ros2'
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.597s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.598s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.598s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.629s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'octomap_server2'
[0.629s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.630s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.630s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.630s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.630s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.672s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.672s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.676s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/octomap_ros2/install
[0.677s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/ws_livox/install
[0.680s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 367 installed packages in /opt/ros/humble
[0.717s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_args' from command line to 'None'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target' from command line to 'None'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_cache' from command line to 'False'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_first' from command line to 'False'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_force_configure' from command line to 'False'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'ament_cmake_args' from command line to 'None'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_cmake_args' from command line to 'None'
[0.816s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.816s] DEBUG:colcon.colcon_core.verb:Building package 'octomap_server2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2', 'merge_install': False, 'path': '/home/<USER>/fastlio2/octomap_ros2', 'symlink_install': False, 'test_result_base': None}
[0.817s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.819s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.820s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/fastlio2/octomap_ros2' with build type 'ament_cmake'
[0.820s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/fastlio2/octomap_ros2'
[0.825s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.825s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.825s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.843s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[0.950s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[0.961s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[0.981s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[0.982s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[0.989s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[0.991s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[0.991s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[0.992s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[0.995s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[0.996s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[0.999s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[1.000s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[1.000s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[1.002s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[1.003s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[1.005s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.005s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[1.006s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[1.006s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.008s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[1.010s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[1.013s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[1.015s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[1.018s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[1.020s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[1.023s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[1.024s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[1.025s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[1.026s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[1.027s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[1.028s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[1.030s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[1.032s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[1.032s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[1.033s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[1.035s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[1.036s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[1.037s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.037s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[1.038s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[1.039s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.040s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[1.041s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[1.043s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[1.044s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[1.045s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[1.046s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[1.048s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.048s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.049s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.049s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.063s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.064s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.064s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.117s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.118s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.ps1'
[1.122s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_ps1.py'
[1.127s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.ps1'
[1.131s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.sh'
[1.132s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_sh.py'
[1.134s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.sh'
[1.137s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.bash'
[1.139s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.bash'
[1.142s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.zsh'
[1.144s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.zsh'
