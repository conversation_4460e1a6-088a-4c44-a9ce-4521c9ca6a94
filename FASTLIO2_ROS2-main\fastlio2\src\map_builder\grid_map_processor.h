#pragma once

#include <memory>
#include <vector>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/segmentation/extract_clusters.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree.h>
#include <Eigen/Dense>
#include <rclcpp/rclcpp.hpp>

namespace fastlio
{

class GridMapProcessor
{
public:
    struct Config
    {
        bool enable_flag = true;           // 是否启用点云分割
        double voxel_size = 0.05;          // 体素滤波大小（米）
        double plane_distance_threshold = 0.02;  // RANSAC平面距离阈值（米）
        int max_iterations = 1000;        // RANSAC最大迭代次数
        double cluster_tolerance = 0.1;   // 欧式聚类距离阈值（米）
        int min_cluster_size = 10;        // 最小聚类点数
        int max_cluster_size = 25000;     // 最大聚类点数
        double ground_height_threshold = 0.2;  // 地面高度阈值（米）
    };

    GridMapProcessor(const Config& config);
    ~GridMapProcessor() = default;

    // 处理点云分割
    void processPointCloud(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud, const Eigen::Vector3d& position);

    // 获取地面点云
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr getGroundCloud() const;

    // 获取非地面点云
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr getNonGroundCloud() const;

    // 获取聚类结果
    std::vector<pcl::PointCloud<pcl::PointXYZINormal>::Ptr> getClusterClouds() const;

private:
    // RANSAC平面分割
    void performRANSACSegmentation(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud);

    // 欧式聚类
    void performEuclideanClustering(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud);

    // 体素滤波
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr applyVoxelFilter(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud);

    Config m_config;

    // 分割结果存储
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr m_ground_cloud;      // 地面点云
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr m_non_ground_cloud;  // 非地面点云
    std::vector<pcl::PointCloud<pcl::PointXYZINormal>::Ptr> m_cluster_clouds;  // 聚类结果

    // PCL分割器
    pcl::SACSegmentation<pcl::PointXYZINormal> m_seg;
    pcl::EuclideanClusterExtraction<pcl::PointXYZINormal> m_ec;
    pcl::VoxelGrid<pcl::PointXYZINormal> m_voxel_filter;
};
} 
