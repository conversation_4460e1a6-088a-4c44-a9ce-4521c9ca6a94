#pragma once

#include <memory>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <Eigen/Dense>
#include <rclcpp/rclcpp.hpp>

namespace fastlio
{

class GridMapProcessor
{
public:
    struct Config
    {
        bool enable_flag = true;           // 是否启用栅格地图
        double resolution = 0.05;          // 栅格地图分辨率（米）
        double global_size = 100.0;         // 全局地图大小（米）
        double local_size = 10.0;          // 局部地图大小（米）
        double ground_rate = 0.3;          // 地面点判定比例（相对高度）
    };

    GridMapProcessor(const Config& config);
    ~GridMapProcessor() = default;

    // 更新栅格地图
    void updateMap(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud, const Eigen::Vector3d& position);

    // 获取2D栅格地图
    nav_msgs::msg::OccupancyGrid::SharedPtr get2DGridMap() const;

private:
    // 判断点是否为地面点
    bool isGroundPoint(const pcl::PointXYZINormal& point) const;

    // 更新栅格状态
    void updateCell(const pcl::PointXYZINormal& point);

    Config m_config;
    std::vector<int8_t> m_global_map_data;    // 全局地图数据
    std::vector<int8_t> m_local_map_data;     // 局部地图数据
    std::vector<int> m_observed_count;        // 观测次数统计
    double m_min_z;                           // 最低高度
    double m_max_z;                           // 最高高度
    Eigen::Vector2d m_map_origin;            // 地图原点
    int m_global_width;                      // 全局地图宽度
    int m_global_height;                     // 全局地图高度
    int m_local_width;                       // 局部地图宽度
    int m_local_height;                      // 局部地图高度
};
} 
