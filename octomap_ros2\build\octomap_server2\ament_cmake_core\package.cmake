set(_AMENT_PACKAGE_NAME "octomap_server2")
set(octomap_server2_VERSION "0.0.0")
set(octomap_server2_MAINTAINER "<PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@krish.neel>")
set(octomap_server2_BUILD_DEPENDS "rclcpp" "rclcpp_components" "rcutils" "rmw" "rmw_implementation_cmake" "std_msgs" "sensor_msgs" "geometry_msgs" "nav_msgs" "visualization_msgs" "pcl_conversions" "pcl_msgs" "octomap_msgs")
set(octomap_server2_BUILDTOOL_DEPENDS "ament_cmake")
set(octomap_server2_BUILD_EXPORT_DEPENDS )
set(octomap_server2_BUILDTOOL_EXPORT_DEPENDS )
set(octomap_server2_EXEC_DEPENDS "launch_ros" "launch_xml" "rclcpp" "rclcpp_components" "rcutils" "rmw" "std_msgs" "sensor_msgs" "geometry_msgs" "nav_msgs" "visualization_msgs" "pcl_conversions" "pcl_msgs" "octomap_msgs")
set(octomap_server2_TEST_DEPENDS )
set(octomap_server2_GROUP_DEPENDS )
set(octomap_server2_MEMBER_OF_GROUPS )
set(octomap_server2_DEPRECATED "")
set(octomap_server2_EXPORT_TAGS)
list(APPEND octomap_server2_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
