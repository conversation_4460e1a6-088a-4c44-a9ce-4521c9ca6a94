#pragma once
#include <Eigen/Eigen>
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include <vector>
#include <queue>

using PointType = pcl::PointXYZI;
using CloudType = pcl::PointCloud<PointType>;
using PointVec = std::vector<PointType, Eigen::aligned_allocator<PointType>>;

using M3D = Eigen::Matrix3d;
using V3D = Eigen::Vector3d;
using M3F = Eigen::Matrix3f;
using V3F = Eigen::Vector3f;
using M4F = Eigen::Matrix4f;
using V4F = Eigen::Vector4f;

// 定义用于自动重定位的变换结果类
struct ResultTransformation {
public:
    ResultTransformation(Eigen::Matrix4f r, float s): result(std::move(r)), score(s) {};

    bool operator<(const ResultTransformation& rhs) const{
        return score < rhs.score;
    }

    Eigen::Matrix4f result;
    float score;
};

// 定义用于自动全局重定位的离散变换类
struct DiscreteTransformation {
public:
    using Points = std::vector<Eigen::Vector2f, Eigen::aligned_allocator<Eigen::Vector2f>>;

    DiscreteTransformation() : level(-1), score(0.0f) {}
    
    DiscreteTransformation(int level, float x, float y, float theta) 
        : level(level), x(x), y(y), theta(theta), score(0.0f) {}

    bool operator<(const DiscreteTransformation& rhs) const {
        return score < rhs.score;
    }

    bool is_leaf() const {
        return level == 0;
    }

    Points transform(const Points& points) const {
        Points transformed(points.size());
        Eigen::Map<const Eigen::Matrix<float, 2, -1>> src(points[0].data(), 2, points.size());
        Eigen::Map<Eigen::Matrix<float, 2, -1>> dst(transformed[0].data(), 2, points.size());

        Eigen::Matrix2f rot = Eigen::Rotation2Df(theta).toRotationMatrix();
        Eigen::Vector2f trans(x, y);

        dst = (rot * src).colwise() + trans;
        return transformed;
    }

    std::vector<DiscreteTransformation> branch(const float& resolution) const {
        std::vector<DiscreteTransformation> b;
        float x_bias = resolution, y_bias = resolution;
        if(x < 0)
            x_bias = -resolution;
        if(y < 0)
            y_bias = -resolution;
        b.reserve(5);
        b.emplace_back(DiscreteTransformation(level - 1, x * 2, y * 2, theta));
        b.emplace_back(DiscreteTransformation(level - 1, x * 2 + x_bias, y * 2, theta));
        b.emplace_back(DiscreteTransformation(level - 1, x * 2, y * 2 + y_bias, theta));
        b.emplace_back(DiscreteTransformation(level - 1, x * 2 + x_bias, y * 2 + y_bias, theta));
        b.emplace_back(DiscreteTransformation(level - 1, x * 2 - x_bias, y * 2 - y_bias, theta));
        return b;
    }

    int level;
    float x;
    float y;
    float theta;
    float score;
};
