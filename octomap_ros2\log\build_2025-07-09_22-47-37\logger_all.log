[0.395s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[0.395s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0xffff90185450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff901848b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff901848b0>>)
[0.858s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.859s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.859s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.859s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.859s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.859s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.859s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/fastlio2/octomap_ros2'
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.861s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.861s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.861s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.861s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.900s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'octomap_server2'
[0.900s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.901s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.901s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.901s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.901s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.955s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/ws_livox/install
[0.958s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 367 installed packages in /opt/ros/humble
[0.994s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_args' from command line to 'None'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target' from command line to 'None'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_cache' from command line to 'False'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_first' from command line to 'False'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_force_configure' from command line to 'False'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'ament_cmake_args' from command line to 'None'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_cmake_args' from command line to 'None'
[1.114s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.115s] DEBUG:colcon.colcon_core.verb:Building package 'octomap_server2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2', 'merge_install': False, 'path': '/home/<USER>/fastlio2/octomap_ros2', 'symlink_install': True, 'test_result_base': None}
[1.115s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.117s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.118s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/fastlio2/octomap_ros2' with build type 'ament_cmake'
[1.118s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/fastlio2/octomap_ros2'
[1.125s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.125s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.125s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.145s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[1.342s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[1.353s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[1.376s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[1.378s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[1.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[1.385s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[1.386s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[1.387s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[1.389s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[1.390s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[1.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[1.392s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[1.393s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[1.394s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[1.395s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[1.396s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.396s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[1.397s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[1.398s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.399s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[1.402s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[1.404s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[1.406s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[1.408s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[1.409s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[1.411s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[1.411s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[1.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[1.414s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[1.414s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[1.416s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[1.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[1.420s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[1.420s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[1.421s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[1.422s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[1.423s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[1.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[1.425s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[1.425s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.426s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[1.428s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[1.429s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[1.431s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[1.432s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[1.434s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[1.434s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.435s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.435s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.436s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.455s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.456s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.456s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.494s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.495s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.ps1'
[1.498s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_ps1.py'
[1.502s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.ps1'
[1.505s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.sh'
[1.507s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_sh.py'
[1.509s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.sh'
[1.511s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.bash'
[1.512s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.bash'
[1.515s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.zsh'
[1.517s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.zsh'
