{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-69d81e6f12f74fb85a0c.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "octomap_server2", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "octomap_server::@6890427a1f51a3e7e1df", "jsonFile": "target-octomap_server-6ed2ea6e301b88a45fa3.json", "name": "octomap_server", "projectIndex": 0}, {"directoryIndex": 0, "id": "octomap_server2::@6890427a1f51a3e7e1df", "jsonFile": "target-octomap_server2-b2867828e1b1ef28f050.json", "name": "octomap_server2", "projectIndex": 0}, {"directoryIndex": 0, "id": "octomap_server2_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-octomap_server2_uninstall-2c4f26dca103a99dc7c9.json", "name": "octomap_server2_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-4e0ab29ae94e710b8b7e.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2", "source": "/home/<USER>/fastlio2/octomap_ros2"}, "version": {"major": 2, "minor": 6}}