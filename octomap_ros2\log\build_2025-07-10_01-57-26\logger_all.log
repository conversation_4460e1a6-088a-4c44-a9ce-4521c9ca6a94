[0.239s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.239s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0xffff7fb15450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff7fb148b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff7fb148b0>>)
[0.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.632s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/fastlio2/octomap_ros2'
[0.633s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.633s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.633s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.633s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.633s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.634s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.634s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.634s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.634s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.666s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'octomap_server2'
[0.666s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.666s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.666s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.666s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.667s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.710s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.710s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.714s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/ws_livox/install
[0.717s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 367 installed packages in /opt/ros/humble
[0.751s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.849s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_args' from command line to 'None'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target' from command line to 'None'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_cache' from command line to 'False'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_first' from command line to 'False'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_force_configure' from command line to 'False'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'ament_cmake_args' from command line to 'None'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_cmake_args' from command line to 'None'
[0.850s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.850s] DEBUG:colcon.colcon_core.verb:Building package 'octomap_server2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2', 'merge_install': False, 'path': '/home/<USER>/fastlio2/octomap_ros2', 'symlink_install': False, 'test_result_base': None}
[0.851s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.853s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.853s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/fastlio2/octomap_ros2' with build type 'ament_cmake'
[0.854s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/fastlio2/octomap_ros2'
[0.859s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.860s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.860s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.883s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[6.852s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[6.860s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[7.012s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[7.023s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[7.055s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[7.059s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[7.067s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[7.072s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[7.073s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[7.075s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[7.077s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[7.079s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[7.084s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[7.084s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[7.085s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[7.087s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[7.088s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[7.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[7.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[7.091s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[7.092s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[7.093s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[7.096s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[7.098s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[7.101s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[7.104s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[7.107s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[7.109s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[7.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[7.112s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[7.113s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[7.113s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[7.115s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[7.116s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[7.117s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[7.117s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[7.118s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[7.119s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[7.120s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[7.121s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[7.122s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[7.123s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[7.124s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[7.125s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[7.128s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[7.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[7.132s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[7.133s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[7.134s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[7.135s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[7.137s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[7.138s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[7.139s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[7.154s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[7.154s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[7.154s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[7.209s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[7.210s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.ps1'
[7.213s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_ps1.py'
[7.222s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.ps1'
[7.227s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.sh'
[7.231s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_sh.py'
[7.233s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.sh'
[7.237s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.bash'
[7.239s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.bash'
[7.242s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.zsh'
[7.244s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.zsh'
