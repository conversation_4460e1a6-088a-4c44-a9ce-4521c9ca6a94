#include "map_builder.h"
MapBuilder::MapBuilder(Config &config, std::shared_ptr<IESKF> kf) : m_config(config), m_kf(kf)
{
    m_imu_processor = std::make_shared<IMUProcessor>(config, kf);
    m_lidar_processor = std::make_shared<LidarProcessor>(config, kf);
    
    // 初始化栅格地图处理器
    fastlio::GridMapProcessor::Config grid_map_config;
    grid_map_config.enable_flag = m_config.grid_map_enable;
    grid_map_config.global_size = m_config.grid_map_global_size;
    grid_map_config.local_size = m_config.grid_map_local_size;
    grid_map_config.resolution = m_config.grid_map_resolution;
    grid_map_config.ground_rate = m_config.grid_map_ground_rate;
    m_grid_map_processor = std::make_shared<fastlio::GridMapProcessor>(grid_map_config);
    
    m_status = BuilderStatus::IMU_INIT;
}

void MapBuilder::process(SyncPackage &package)
{
    if (m_status == BuilderStatus::IMU_INIT)
    {
        if (m_imu_processor->initialize(package))
            m_status = BuilderStatus::MAP_INIT;
        return;
    }

    m_imu_processor->undistort(package);

    if (m_status == BuilderStatus::MAP_INIT)
    {
        CloudType::Ptr cloud_world = LidarProcessor::transformCloud(package.cloud, m_lidar_processor->r_wl(), m_lidar_processor->t_wl());
        m_lidar_processor->initCloudMap(cloud_world->points);
        m_status = BuilderStatus::MAPPING;
        return;
    }
    
    m_lidar_processor->process(package);
    
    // 更新栅格地图
    if (m_status == BuilderStatus::MAPPING && m_grid_map_processor)
    {
        m_grid_map_processor->updateMap(package.cloud, m_kf->x().t_wi);
    }
}