#include "map_builder.h"
MapBuilder::MapBuilder(Config &config, std::shared_ptr<IESKF> kf) : m_config(config), m_kf(kf)
{
    m_imu_processor = std::make_shared<IMUProcessor>(config, kf);
    m_lidar_processor = std::make_shared<LidarProcessor>(config, kf);
    
    // 初始化点云分割处理器
    fastlio::GridMapProcessor::Config point_cloud_config;
    point_cloud_config.enable_flag = m_config.point_cloud_segmentation_enable;
    point_cloud_config.voxel_size = m_config.voxel_size;
    point_cloud_config.plane_distance_threshold = m_config.plane_distance_threshold;
    point_cloud_config.max_iterations = m_config.max_iterations;
    point_cloud_config.cluster_tolerance = m_config.cluster_tolerance;
    point_cloud_config.min_cluster_size = m_config.min_cluster_size;
    point_cloud_config.max_cluster_size = m_config.max_cluster_size;
    point_cloud_config.ground_height_threshold = m_config.ground_height_threshold;
    m_grid_map_processor = std::make_shared<fastlio::GridMapProcessor>(point_cloud_config);
    
    m_status = BuilderStatus::IMU_INIT;
}

void MapBuilder::process(SyncPackage &package)
{
    if (m_status == BuilderStatus::IMU_INIT)
    {
        if (m_imu_processor->initialize(package))
            m_status = BuilderStatus::MAP_INIT;
        return;
    }

    m_imu_processor->undistort(package);

    if (m_status == BuilderStatus::MAP_INIT)
    {
        CloudType::Ptr cloud_world = LidarProcessor::transformCloud(package.cloud, m_lidar_processor->r_wl(), m_lidar_processor->t_wl());
        m_lidar_processor->initCloudMap(cloud_world->points);
        m_status = BuilderStatus::MAPPING;
        return;
    }
    
    m_lidar_processor->process(package);
    
    // 处理点云分割
    if (m_status == BuilderStatus::MAPPING && m_grid_map_processor)
    {
        m_grid_map_processor->processPointCloud(package.cloud, m_kf->x().t_wi);
    }
}