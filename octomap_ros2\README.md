ros2 run nav2_map_server map_saver_server

ros2 lifecycle set /map_saver configure
ros2 lifecycle set /map_saver activate

ros2 service call /map_saver/save_map nav2_msgs/srv/SaveMap \
  "{map_topic: '/projected_map', \
    map_url: '/home/<USER>/testmap_1', \
    image_format: 'pgm', \
    map_mode: 'trinary', \
    free_thresh: 0.25, \
    occupied_thresh: 0.65}"








# octomap_server_ros2

octomapping

#### 下载
首先需要安装[octomap](https://github.com/OctoMap/octomap.git) 
也可以二进制安装

```
sudo apt install ros-humble-octomap

```
安装pcl
```
sudo apt install ros-humble-perception-pcl

```
下载其他
```
vcs import . < deps.repos
```

### 编译项目

```
colco build --packages-up-to octomap_server2
```

### 启动
修改lacunch文件内 `cloud_in` 为真实输入点云话题
```
ros2 launch octomap_server2 octomap_server_launch.py
```
