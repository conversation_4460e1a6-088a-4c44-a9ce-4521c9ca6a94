[0.314s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.314s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0xffff958d5450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff958d48b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff958d48b0>>)
[0.731s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.731s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.731s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.731s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.731s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.732s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.732s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/fastlio2/octomap_ros2'
[0.732s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.732s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.732s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.733s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.733s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.733s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.733s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.733s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.733s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.765s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'octomap_server2'
[0.765s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.766s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.766s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.766s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.766s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.808s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.808s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.812s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/octomap_ros2/install
[0.813s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/ws_livox/install
[0.816s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 367 installed packages in /opt/ros/humble
[0.854s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.955s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_args' from command line to 'None'
[0.955s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target' from command line to 'None'
[0.955s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.955s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_cache' from command line to 'False'
[0.955s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_first' from command line to 'False'
[0.955s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_force_configure' from command line to 'False'
[0.956s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'ament_cmake_args' from command line to 'None'
[0.956s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_cmake_args' from command line to 'None'
[0.956s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.956s] DEBUG:colcon.colcon_core.verb:Building package 'octomap_server2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2', 'merge_install': False, 'path': '/home/<USER>/fastlio2/octomap_ros2', 'symlink_install': False, 'test_result_base': None}
[0.956s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.959s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.959s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/fastlio2/octomap_ros2' with build type 'ament_cmake'
[0.960s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/fastlio2/octomap_ros2'
[0.965s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.966s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.966s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.994s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[1.184s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[1.195s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[1.229s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[1.232s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[1.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[1.245s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[1.245s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[1.246s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[1.247s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[1.256s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[1.260s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[1.260s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[1.261s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[1.262s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[1.263s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[1.263s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.264s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[1.264s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[1.265s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.266s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[1.267s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[1.269s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[1.271s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[1.273s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[1.275s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[1.277s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[1.277s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[1.278s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[1.279s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[1.280s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[1.281s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[1.282s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[1.284s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[1.284s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[1.285s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[1.286s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[1.286s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[1.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[1.289s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[1.289s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[1.291s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[1.292s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[1.293s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[1.294s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[1.295s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[1.296s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.297s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.297s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.298s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.314s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.315s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.315s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.360s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.362s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.ps1'
[1.364s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_ps1.py'
[1.368s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.ps1'
[1.371s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.sh'
[1.373s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_sh.py'
[1.376s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.sh'
[1.380s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.bash'
[1.382s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.bash'
[1.385s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.zsh'
[1.387s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.zsh'
