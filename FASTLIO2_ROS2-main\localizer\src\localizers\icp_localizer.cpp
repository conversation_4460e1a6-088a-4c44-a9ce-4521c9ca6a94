#include "icp_localizer.h"
#include <pcl/segmentation/extract_clusters.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/filters/extract_indices.h>

ICPLocalizer::ICPLocalizer(const ICPConfig &config) : m_config(config)
{
    m_refine_inp.reset(new CloudType);
    m_refine_tgt.reset(new CloudType);
    m_rough_inp.reset(new CloudType);
    m_rough_tgt.reset(new CloudType);
    
    // 初始化网格地图金字塔
    m_grid_map_pyramid = grid_map::GridMap(m_pyramid_level_names);
    
    // 初始化最后一次重定位时间为当前时间
    m_last_relocalization_time = std::chrono::steady_clock::now();
}

bool ICPLocalizer::loadMap(const std::string &path)
{
    if (!std::filesystem::exists(path))
    {
        std::cerr << "Map file not found: " << path << std::endl;
        return false;
    }
    pcl::PCDReader reader;
    CloudType::Ptr cloud(new CloudType);
    reader.read(path, *cloud);
    if (m_config.refine_map_resolution > 0)
    {
        m_voxel_filter.setLeafSize(m_config.refine_map_resolution, m_config.refine_map_resolution, m_config.refine_map_resolution);
        m_voxel_filter.setInputCloud(cloud);
        m_voxel_filter.filter(*m_refine_tgt);
    }
    else
    {
        pcl::copyPointCloud(*cloud, *m_refine_tgt);
    }

    if (m_config.rough_map_resolution > 0)
    {
        m_voxel_filter.setLeafSize(m_config.rough_map_resolution, m_config.rough_map_resolution, m_config.rough_map_resolution);
        m_voxel_filter.setInputCloud(cloud);
        m_voxel_filter.filter(*m_rough_tgt);
    }
    else
    {
        pcl::copyPointCloud(*cloud, *m_rough_tgt);
    }
    
    // 创建网格地图金字塔用于自动全局重定位
    if (m_config.auto_global_relocalization_enable) {
        createGridMapPyramid(MAX_PYRAMID_LEVEL);
    }
    
    return true;
}

void ICPLocalizer::setInput(const CloudType::Ptr &cloud)
{
    if (m_config.refine_scan_resolution > 0)
    {
        m_voxel_filter.setLeafSize(m_config.refine_scan_resolution, m_config.refine_scan_resolution, m_config.refine_scan_resolution);
        m_voxel_filter.setInputCloud(cloud);
        m_voxel_filter.filter(*m_refine_inp);
    }
    else
    {
        pcl::copyPointCloud(*cloud, *m_refine_inp);
    }

    if (m_config.rough_scan_resolution > 0)
    {
        m_voxel_filter.setLeafSize(m_config.rough_scan_resolution, m_config.rough_scan_resolution, m_config.rough_scan_resolution);
        m_voxel_filter.setInputCloud(cloud);
        m_voxel_filter.filter(*m_rough_inp);
    }
    else
    {
        pcl::copyPointCloud(*cloud, *m_rough_inp);
    }
}

bool ICPLocalizer::align(M4F &guess)
{
    CloudType::Ptr aligned_cloud(new CloudType);
    if (m_refine_tgt->size() == 0 || m_rough_tgt->size() == 0)
        return false;
    m_rough_icp.setMaximumIterations(m_config.rough_max_iteration);
    m_rough_icp.setInputSource(m_rough_inp);
    m_rough_icp.setInputTarget(m_rough_tgt);
    m_rough_icp.align(*aligned_cloud, guess);
    if (!m_rough_icp.hasConverged() || m_rough_icp.getFitnessScore() > m_config.rough_score_thresh)
        return false;
    m_refine_icp.setMaximumIterations(m_config.refine_max_iteration);
    m_refine_icp.setInputSource(m_refine_inp);
    m_refine_icp.setInputTarget(m_refine_tgt);
    m_refine_icp.align(*aligned_cloud, m_rough_icp.getFinalTransformation());
    if (!m_refine_icp.hasConverged() || m_refine_icp.getFitnessScore() > m_config.refine_score_thresh)
        return false;
    guess = m_refine_icp.getFinalTransformation();
    return true;
}

// 创建用于自动全局重定位的网格地图金字塔
void ICPLocalizer::createGridMapPyramid(int max_level)
{
    // 设置网格地图参数
    float map_resolution = m_config.auto_global_resolution;
    float map_length_x = std::abs(m_config.auto_global_search_range_x[1] - m_config.auto_global_search_range_x[0]);
    float map_length_y = std::abs(m_config.auto_global_search_range_y[1] - m_config.auto_global_search_range_y[0]);
    
    // 初始化地图
    m_grid_map_pyramid.setFrameId("map");
    m_grid_map_pyramid.setGeometry(grid_map::Length(map_length_x, map_length_y), map_resolution);
    
    // 提取地图点云中的地面点和墙面点
    pcl::ModelCoefficients::Ptr coefficients(new pcl::ModelCoefficients);
    pcl::PointIndices::Ptr inliers(new pcl::PointIndices);
    pcl::SACSegmentation<PointType> seg;
    seg.setOptimizeCoefficients(true);
    seg.setModelType(pcl::SACMODEL_PLANE);
    seg.setMethodType(pcl::SAC_RANSAC);
    seg.setDistanceThreshold(0.1);
    seg.setInputCloud(m_refine_tgt);
    seg.segment(*inliers, *coefficients);
    
    // 提取地面点
    pcl::ExtractIndices<PointType> extract;
    CloudType::Ptr ground_cloud(new CloudType);
    extract.setInputCloud(m_refine_tgt);
    extract.setIndices(inliers);
    extract.setNegative(false);
    extract.filter(*ground_cloud);
    
    // 提取非地面点(墙面点)
    CloudType::Ptr wall_cloud(new CloudType);
    extract.setNegative(true);
    extract.filter(*wall_cloud);
    
    // 将点云填充到地图中
    for (const auto& point : ground_cloud->points) {
        grid_map::Position position(point.x, point.y);
        if (m_grid_map_pyramid.isInside(position)) {
            m_grid_map_pyramid.atPosition("level0", position) = -1; // 地面点标记为-1
        }
    }
    
    for (const auto& point : wall_cloud->points) {
        grid_map::Position position(point.x, point.y);
        if (m_grid_map_pyramid.isInside(position)) {
            m_grid_map_pyramid.atPosition("level0", position) = 1; // 墙面点标记为1
        }
    }
    
    // 构建金字塔层级
    for (int i = 1; i < max_level; ++i) {
        // 对当前层进行降采样
        for (grid_map::GridMapIterator it(m_grid_map_pyramid); !it.isPastEnd(); ++it) {
            grid_map::Index index = *it;
            grid_map::Position position;
            m_grid_map_pyramid.getPosition(index, position);
            
            // 计算在高层的位置和索引
            grid_map::Position position_higher(position.x() / 2, position.y() / 2);
            grid_map::Index index_higher;
            if (m_grid_map_pyramid.getIndex(position_higher, index_higher)) {
                // 合并低层的数据到高层
                float value = m_grid_map_pyramid.at(m_pyramid_level_names[i-1], index);
                if (value == -1) { // 如果是地面点
                    m_grid_map_pyramid.at(m_pyramid_level_names[i], index_higher) = -1;
                } else if (value == 1) { // 如果是墙面点
                    m_grid_map_pyramid.at(m_pyramid_level_names[i], index_higher) = 1;
                }
            }
        }
    }
}

// 将点云转换为2D点用于评分
DiscreteTransformation::Points ICPLocalizer::convertCloudTo2DPoints(const CloudType::Ptr &cloud, bool is_ground)
{
    DiscreteTransformation::Points points;
    points.reserve(cloud->size());
    
    // 提取地面点或墙面点
    pcl::ModelCoefficients::Ptr coefficients(new pcl::ModelCoefficients);
    pcl::PointIndices::Ptr inliers(new pcl::PointIndices);
    pcl::SACSegmentation<PointType> seg;
    seg.setOptimizeCoefficients(true);
    seg.setModelType(pcl::SACMODEL_PLANE);
    seg.setMethodType(pcl::SAC_RANSAC);
    seg.setDistanceThreshold(0.1);
    seg.setInputCloud(cloud);
    seg.segment(*inliers, *coefficients);
    
    CloudType::Ptr filtered_cloud(new CloudType);
    pcl::ExtractIndices<PointType> extract;
    extract.setInputCloud(cloud);
    extract.setIndices(inliers);
    extract.setNegative(!is_ground); // 如果要地面点，则不取反；如果要墙面点，则取反
    extract.filter(*filtered_cloud);
    
    // 转换为2D点
    for (const auto& point : filtered_cloud->points) {
        points.emplace_back(Eigen::Vector2f(point.x, point.y));
    }
    
    return points;
}

// 计算变换的评分
float ICPLocalizer::calculateScore(const DiscreteTransformation &trans, 
                                   const DiscreteTransformation::Points &ground_points,
                                   const DiscreteTransformation::Points &wall_points)
{
    auto transformed_ground = trans.transform(ground_points);
    auto transformed_wall = trans.transform(wall_points);
    float score = 0.0f;
    float gain = std::pow(2, trans.level);
    
    // 评估地面点
    for (auto& point : transformed_ground) {
        grid_map::Position position(point.x(), point.y());
        if (!m_grid_map_pyramid.isInside(position))
            continue;
            
        if (m_grid_map_pyramid.atPosition(m_pyramid_level_names[trans.level], position) == -1) {
            score += 0.02f * gain; // 地面点匹配奖励
        } else {
            score -= 0.01f * gain; // 不匹配惩罚
        }
    }
    
    // 评估墙面点
    for (auto& point : transformed_wall) {
        grid_map::Position position(point.x(), point.y());
        if (!m_grid_map_pyramid.isInside(position))
            continue;
            
        if (m_grid_map_pyramid.atPosition(m_pyramid_level_names[trans.level], position) == 1) {
            score += 0.015f * gain; // 墙面点匹配奖励
        } else {
            score -= 0.01f * gain; // 不匹配惩罚
        }
    }
    
    return score;
}

// 自动全局重定位
bool ICPLocalizer::autoGlobalRelocalization(M4F &guess)
{
    if (!m_config.auto_global_relocalization_enable || m_auto_global_success) {
        return false;
    }
    
    std::cout << "Starting auto global relocalization..." << std::endl;
    
    // 将输入点云转换为2D点
    DiscreteTransformation::Points ground_points = convertCloudTo2DPoints(m_refine_inp, true);
    DiscreteTransformation::Points wall_points = convertCloudTo2DPoints(m_refine_inp, false);
    
    if (ground_points.empty() && wall_points.empty()) {
        std::cerr << "No points for global relocalization!" << std::endl;
        return false;
    }
    
    // 构建候选变换队列
    std::priority_queue<DiscreteTransformation> trans_queue;
    std::vector<DiscreteTransformation> trans_vector;
    
    // 在整个搜索范围内生成初始候选变换
    for (float x = m_config.auto_global_search_range_x[0]; x <= m_config.auto_global_search_range_x[1]; 
         x += m_config.auto_global_resolution * std::pow(2, MAX_PYRAMID_LEVEL-1)) {
        for (float y = m_config.auto_global_search_range_y[0]; y <= m_config.auto_global_search_range_y[1]; 
             y += m_config.auto_global_resolution * std::pow(2, MAX_PYRAMID_LEVEL-1)) {
            for (float theta = m_config.auto_global_search_range_yaw[0]; theta <= m_config.auto_global_search_range_yaw[1]; 
                 theta += m_config.auto_global_angle_resolution) {
                trans_vector.emplace_back(DiscreteTransformation(MAX_PYRAMID_LEVEL-1, x, y, theta));
                
                // 计算评分
                float score = calculateScore(trans_vector.back(), ground_points, wall_points);
                trans_vector.back().score = score;
                trans_queue.push(trans_vector.back());
            }
        }
    }
    
    // 存储最佳结果
    std::priority_queue<ResultTransformation> result_queue;
    
    // 分支定界搜索
    int iterate_count = 0;
    while (iterate_count < m_config.auto_global_max_iterations) {
        iterate_count++;
        std::cout << "Global relocalization iteration " << iterate_count << std::endl;
        
        float best_score = 0.0f;
        DiscreteTransformation best_trans;
        
        // 分支定界搜索
        while (!trans_queue.empty()) {
            auto trans = trans_queue.top();
            trans_queue.pop();
            
            if (trans.score < best_score) {
                continue;
            }
            
            if (trans.is_leaf()) {
                // 如果是叶节点，更新最佳结果
                if (trans.score > best_score) {
                    best_trans = trans;
                    best_score = trans.score;
                }
            } else {
                // 如果不是叶节点，进行分支
                auto branches = trans.branch(m_config.auto_global_resolution);
                for (auto& branch : branches) {
                    grid_map::Position position(branch.x, branch.y);
                    if (!m_grid_map_pyramid.isInside(position)) {
                        continue;
                    }
                    
                    float score = calculateScore(branch, ground_points, wall_points);
                    branch.score = score;
                    trans_queue.push(branch);
                }
            }
        }
        
        // 使用最佳变换作为ICP的初始猜测
        if (best_score > 0) {
            M4F init_guess = M4F::Identity();
            init_guess(0, 0) = std::cos(best_trans.theta);
            init_guess(0, 1) = -std::sin(best_trans.theta);
            init_guess(1, 0) = std::sin(best_trans.theta);
            init_guess(1, 1) = std::cos(best_trans.theta);
            init_guess(0, 3) = best_trans.x;
            init_guess(1, 3) = best_trans.y;
            
            // 使用ICP进行精确定位
            CloudType::Ptr aligned_cloud(new CloudType);
            m_refine_icp.setMaximumIterations(m_config.refine_max_iteration * 2); // 增加迭代次数
            m_refine_icp.setInputSource(m_refine_inp);
            m_refine_icp.setInputTarget(m_refine_tgt);
            m_refine_icp.align(*aligned_cloud, init_guess);
            
            if (m_refine_icp.hasConverged() && m_refine_icp.getFitnessScore() < m_config.refine_score_thresh) {
                // 重定位成功
                guess = m_refine_icp.getFinalTransformation();
                m_auto_global_success = true;
                m_last_relocalization_time = std::chrono::steady_clock::now();
                m_last_relocalization_pose = guess;
                std::cout << "Global relocalization success! Score: " << m_refine_icp.getFitnessScore() << std::endl;
                return true;
            }
            
            // 保存结果
            result_queue.push(ResultTransformation(m_refine_icp.getFinalTransformation(), m_refine_icp.getFitnessScore()));
        }
        
        // 如果没有找到好的解，使用之前的最佳结果
        if (result_queue.empty()) {
            return false;
        }
        
        // 使用最佳结果
        auto best_result = result_queue.top();
        if (best_result.score < m_config.auto_global_score_threshold) {
            guess = best_result.result;
            m_auto_global_success = true;
            m_last_relocalization_time = std::chrono::steady_clock::now();
            m_last_relocalization_pose = guess;
            std::cout << "Global relocalization with stored result! Score: " << best_result.score << std::endl;
            return true;
        }
    }
    
    return false;
}

// 检查是否需要触发重定位
bool ICPLocalizer::checkNeedRelocalization(const M4F &current_pose, const std::chrono::steady_clock::time_point &current_time)
{
    if (!m_config.auto_relocalization_enable || !m_auto_global_success) {
        return false;
    }
    
    // 计算距离上次重定位的时间
    auto time_diff = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - m_last_relocalization_time).count();
        
    // 计算距离上次重定位的位置
    Eigen::Vector3f last_pos(m_last_relocalization_pose(0, 3), m_last_relocalization_pose(1, 3), m_last_relocalization_pose(2, 3));
    Eigen::Vector3f current_pos(current_pose(0, 3), current_pose(1, 3), current_pose(2, 3));
    float distance = (current_pos - last_pos).norm();
    
    // 判断是否需要重定位
    bool need_relocalization = false;
    
    // 如果距离上次重定位时间超过阈值
    if (time_diff > m_config.auto_relocalization_time_threshold) {
        std::cout << "Time since last relocalization (" << time_diff << "s) exceeds threshold, triggering relocalization" << std::endl;
        need_relocalization = true;
    }
    
    // 如果距离上次重定位位置超过阈值
    if (distance > m_config.auto_relocalization_distance_threshold) {
        std::cout << "Distance since last relocalization (" << distance << "m) exceeds threshold, triggering relocalization" << std::endl;
        need_relocalization = true;
    }
    
    if (need_relocalization) {
        // 更新重定位状态
        m_last_relocalization_time = current_time;
        m_last_relocalization_pose = current_pose;
    }
    
    return need_relocalization;
}