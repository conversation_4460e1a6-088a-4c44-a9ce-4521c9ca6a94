-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclcpp_components: 16.0.12 (/opt/ros/humble/share/rclcpp_components/cmake)
-- Checking for module 'eigen3'
--   Found eigen3, version 3.4.0
-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found suitable version "1.74.0", minimum required is "1.65.0") found components: system filesystem date_time iostreams serialization 
-- Checking for module 'flann'
--   Found flann, version 1.9.1
-- Found FLANN: /usr/lib/aarch64-linux-gnu/libflann_cpp.so  
-- FLANN found (include: /usr/include, lib: /usr/lib/aarch64-linux-gnu/libflann_cpp.so)
-- Found GLEW: /usr/lib/aarch64-linux-gnu/libGLEW.so  
-- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so  found components: OpenGL GLX 
-- Found MPI_C: /usr/lib/aarch64-linux-gnu/libmpi.so (found version "3.1") 
-- Found MPI: TRUE (found version "3.1") found components: C 
-- Found JsonCpp: /usr/lib/aarch64-linux-gnu/libjsoncpp.so (found suitable version "1.9.5", minimum required is "0.7.0") 
-- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")  
-- Found PNG: /usr/lib/aarch64-linux-gnu/libpng.so (found version "1.6.37") 
-- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") 
-- Found X11: /usr/include   
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
-- Looking for gethostbyname
-- Looking for gethostbyname - found
-- Looking for connect
-- Looking for connect - found
-- Looking for remove
-- Looking for remove - found
-- Looking for shmat
-- Looking for shmat - found
-- Looking for IceConnectionNumber in ICE
-- Looking for IceConnectionNumber in ICE - found
-- Found EXPAT: /usr/lib/aarch64-linux-gnu/libexpat.so (found version "2.4.7") 
-- Found double-conversion: /usr/lib/aarch64-linux-gnu/libdouble-conversion.so  
-- Found LZ4: /usr/lib/aarch64-linux-gnu/liblz4.so (found version "1.9.3") 
-- Found LZMA: /usr/lib/aarch64-linux-gnu/liblzma.so (found version "5.2.5") 
-- Found JPEG: /usr/lib/aarch64-linux-gnu/libjpeg.so (found version "80") 
-- Found TIFF: /usr/lib/aarch64-linux-gnu/libtiff.so (found version "4.3.0")  
-- Found Freetype: /usr/lib/aarch64-linux-gnu/libfreetype.so (found version "2.11.1") 
-- Found utf8cpp: /usr/include/utf8cpp  
-- Checking for module 'libusb-1.0'
--   Found libusb-1.0, version 1.0.25
-- Found libusb: /usr/lib/aarch64-linux-gnu/libusb-1.0.so  
-- Found OpenNI: /usr/lib/libOpenNI.so;libusb::libusb (found version "1.5.4.0") 
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Found OpenNI2: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb (found version "2.2.0.33") 
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- looking for PCL_COMMON
-- Found PCL_COMMON: /usr/lib/aarch64-linux-gnu/libpcl_common.so  
-- looking for PCL_KDTREE
-- Found PCL_KDTREE: /usr/lib/aarch64-linux-gnu/libpcl_kdtree.so  
-- looking for PCL_OCTREE
-- Found PCL_OCTREE: /usr/lib/aarch64-linux-gnu/libpcl_octree.so  
-- looking for PCL_SEARCH
-- Found PCL_SEARCH: /usr/lib/aarch64-linux-gnu/libpcl_search.so  
-- looking for PCL_SAMPLE_CONSENSUS
-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/aarch64-linux-gnu/libpcl_sample_consensus.so  
-- looking for PCL_FILTERS
-- Found PCL_FILTERS: /usr/lib/aarch64-linux-gnu/libpcl_filters.so  
-- looking for PCL_2D
-- Found PCL_2D: /usr/include/pcl-1.12  
-- looking for PCL_GEOMETRY
-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
-- looking for PCL_IO
-- Found PCL_IO: /usr/lib/aarch64-linux-gnu/libpcl_io.so  
-- looking for PCL_FEATURES
-- Found PCL_FEATURES: /usr/lib/aarch64-linux-gnu/libpcl_features.so  
-- looking for PCL_ML
-- Found PCL_ML: /usr/lib/aarch64-linux-gnu/libpcl_ml.so  
-- looking for PCL_SEGMENTATION
-- Found PCL_SEGMENTATION: /usr/lib/aarch64-linux-gnu/libpcl_segmentation.so  
-- looking for PCL_VISUALIZATION
-- Found PCL_VISUALIZATION: /usr/lib/aarch64-linux-gnu/libpcl_visualization.so  
-- looking for PCL_SURFACE
-- Found PCL_SURFACE: /usr/lib/aarch64-linux-gnu/libpcl_surface.so  
-- looking for PCL_REGISTRATION
-- Found PCL_REGISTRATION: /usr/lib/aarch64-linux-gnu/libpcl_registration.so  
-- looking for PCL_KEYPOINTS
-- Found PCL_KEYPOINTS: /usr/lib/aarch64-linux-gnu/libpcl_keypoints.so  
-- looking for PCL_TRACKING
-- Found PCL_TRACKING: /usr/lib/aarch64-linux-gnu/libpcl_tracking.so  
-- looking for PCL_RECOGNITION
-- Found PCL_RECOGNITION: /usr/lib/aarch64-linux-gnu/libpcl_recognition.so  
-- looking for PCL_STEREO
-- Found PCL_STEREO: /usr/lib/aarch64-linux-gnu/libpcl_stereo.so  
-- looking for PCL_APPS
-- Found PCL_APPS: /usr/lib/aarch64-linux-gnu/libpcl_apps.so  
-- looking for PCL_IN_HAND_SCANNER
-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
-- looking for PCL_MODELER
-- Found PCL_MODELER: /usr/include/pcl-1.12  
-- looking for PCL_POINT_CLOUD_EDITOR
-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
-- looking for PCL_OUTOFCORE
-- Found PCL_OUTOFCORE: /usr/lib/aarch64-linux-gnu/libpcl_outofcore.so  
-- looking for PCL_PEOPLE
-- Found PCL_PEOPLE: /usr/lib/aarch64-linux-gnu/libpcl_people.so  
-- Found PCL: pcl_common;pcl_kdtree;pcl_octree;pcl_search;pcl_sample_consensus;pcl_filters;pcl_io;pcl_features;pcl_ml;pcl_segmentation;pcl_visualization;pcl_surface;pcl_registration;pcl_keypoints;pcl_tracking;pcl_recognition;pcl_stereo;pcl_apps;pcl_outofcore;pcl_people;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;/usr/lib/libOpenNI.so;libusb::libusb;/usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::GUISupportQt;FLANN::FLANN;QHULL::QHULL (Required is at least version "1.10") 
-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)
-- Found std_srvs: 4.8.0 (/opt/ros/humble/share/std_srvs/cmake)
-- Found OpenMP_C: -fopenmp (found version "4.5") 
-- Found OpenMP_CXX: -fopenmp (found version "4.5") 
-- Found OpenMP: TRUE (found version "4.5")  
-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
-- Found octomap_msgs: 2.0.1 (/opt/ros/humble/share/octomap_msgs/cmake)
-- Found tf2: 0.25.13 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_ros: 0.25.13 (/opt/ros/humble/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Configuring done (8.9s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[ 16%] [32mBuilding CXX object CMakeFiles/octomap_server.dir/rclcpp_components/node_main_octomap_server.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/conversions.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/transforms.cpp.o[0m
[ 83%] [32m[1mLinking CXX executable octomap_server[0m
[ 83%] Built target octomap_server
[100%] [32m[1mLinking CXX shared library liboctomap_server2.so[0m
[100%] Built target octomap_server2
-- Install configuration: ""
-- Execute custom install script
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake
-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml
