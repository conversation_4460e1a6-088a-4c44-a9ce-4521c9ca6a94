[0.000000] (-) TimerEvent: {}
[0.001686] (octomap_server2) JobQueued: {'identifier': 'octomap_server2', 'dependencies': OrderedDict()}
[0.002315] (octomap_server2) JobStarted: {'identifier': 'octomap_server2'}
[0.024396] (octomap_server2) JobProgress: {'identifier': 'octomap_server2', 'progress': 'cmake'}
[0.024833] (octomap_server2) JobProgress: {'identifier': 'octomap_server2', 'progress': 'build'}
[0.024996] (octomap_server2) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', '--', '-j6', '-l6'], 'cwd': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'nvidia'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('JETSON_L4T', '36.3.0'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin NX Engineering Reference Developer Kit'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1734'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'nvidia'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (8GB ram)'), ('JETSON_SERIAL_NUMBER', '1421224256350'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'nvidia'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/nvidia-desktop:@/tmp/.ICE-unix/1734,unix/nvidia-desktop:/tmp/.ICE-unix/1734'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ba536020_4df3_40ed_88e6_2aa3b1bbae09'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('GNOME_TERMINAL_SERVICE', ':1.126'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.0'), ('CMAKE_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0003')]), 'shell': False}
[0.092010] (octomap_server2) StdoutLine: {'line': b'[ 33%] Built target octomap_server\n'}
[0.098877] (-) TimerEvent: {}
[0.139353] (octomap_server2) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.o\x1b[0m\n'}
[0.199078] (-) TimerEvent: {}
[0.299816] (-) TimerEvent: {}
[0.400690] (-) TimerEvent: {}
[0.501536] (-) TimerEvent: {}
[0.602412] (-) TimerEvent: {}
[0.703310] (-) TimerEvent: {}
[0.804183] (-) TimerEvent: {}
[0.905011] (-) TimerEvent: {}
[1.005874] (-) TimerEvent: {}
[1.106710] (-) TimerEvent: {}
[1.207555] (-) TimerEvent: {}
[1.308449] (-) TimerEvent: {}
[1.409271] (-) TimerEvent: {}
[1.510070] (-) TimerEvent: {}
[1.610739] (-) TimerEvent: {}
[1.711422] (-) TimerEvent: {}
[1.812092] (-) TimerEvent: {}
[1.912782] (-) TimerEvent: {}
[2.013455] (-) TimerEvent: {}
[2.114136] (-) TimerEvent: {}
[2.214830] (-) TimerEvent: {}
[2.315523] (-) TimerEvent: {}
[2.416236] (-) TimerEvent: {}
[2.519487] (-) TimerEvent: {}
[2.620173] (-) TimerEvent: {}
[2.720842] (-) TimerEvent: {}
[2.821531] (-) TimerEvent: {}
[2.922213] (-) TimerEvent: {}
[3.025297] (-) TimerEvent: {}
[3.125946] (-) TimerEvent: {}
[3.226644] (-) TimerEvent: {}
[3.327288] (-) TimerEvent: {}
[3.427940] (-) TimerEvent: {}
[3.529997] (-) TimerEvent: {}
[3.630838] (-) TimerEvent: {}
[3.732106] (-) TimerEvent: {}
[3.832919] (-) TimerEvent: {}
[3.933649] (-) TimerEvent: {}
[4.034402] (-) TimerEvent: {}
[4.135123] (-) TimerEvent: {}
[4.235935] (-) TimerEvent: {}
[4.336691] (-) TimerEvent: {}
[4.437497] (-) TimerEvent: {}
[4.538240] (-) TimerEvent: {}
[4.603568] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/conversions.h:50\x1b[m\x1b[K,\n'}
[4.604140] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:51\x1b[m\x1b[K,\n'}
[4.604322] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[4.604462] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.604632] (octomap_server2) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[4.604902] (octomap_server2) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[4.638435] (-) TimerEvent: {}
[4.739144] (-) TimerEvent: {}
[4.839858] (-) TimerEvent: {}
[4.940536] (-) TimerEvent: {}
[5.041271] (-) TimerEvent: {}
[5.141994] (-) TimerEvent: {}
[5.242668] (-) TimerEvent: {}
[5.343346] (-) TimerEvent: {}
[5.444050] (-) TimerEvent: {}
[5.544735] (-) TimerEvent: {}
[5.645456] (-) TimerEvent: {}
[5.746216] (-) TimerEvent: {}
[5.846974] (-) TimerEvent: {}
[5.947707] (-) TimerEvent: {}
[6.048685] (-) TimerEvent: {}
[6.149463] (-) TimerEvent: {}
[6.250322] (-) TimerEvent: {}
[6.351122] (-) TimerEvent: {}
[6.451863] (-) TimerEvent: {}
[6.552633] (-) TimerEvent: {}
[6.653409] (-) TimerEvent: {}
[6.754297] (-) TimerEvent: {}
[6.855869] (-) TimerEvent: {}
[6.957909] (-) TimerEvent: {}
[7.058877] (-) TimerEvent: {}
[7.159677] (-) TimerEvent: {}
[7.260563] (-) TimerEvent: {}
[7.361443] (-) TimerEvent: {}
[7.462323] (-) TimerEvent: {}
[7.563607] (-) TimerEvent: {}
[7.664576] (-) TimerEvent: {}
[7.765450] (-) TimerEvent: {}
[7.866255] (-) TimerEvent: {}
[7.967082] (-) TimerEvent: {}
[8.067895] (-) TimerEvent: {}
[8.168857] (-) TimerEvent: {}
[8.269832] (-) TimerEvent: {}
[8.371046] (-) TimerEvent: {}
[8.471938] (-) TimerEvent: {}
[8.572748] (-) TimerEvent: {}
[8.673699] (-) TimerEvent: {}
[8.774412] (-) TimerEvent: {}
[8.875279] (-) TimerEvent: {}
[8.976026] (-) TimerEvent: {}
[9.076862] (-) TimerEvent: {}
[9.177725] (-) TimerEvent: {}
[9.278472] (-) TimerEvent: {}
[9.379198] (-) TimerEvent: {}
[9.479912] (-) TimerEvent: {}
[9.580700] (-) TimerEvent: {}
[9.681521] (-) TimerEvent: {}
[9.782301] (-) TimerEvent: {}
[9.883013] (-) TimerEvent: {}
[9.983765] (-) TimerEvent: {}
[10.084919] (-) TimerEvent: {}
[10.185727] (-) TimerEvent: {}
[10.286456] (-) TimerEvent: {}
[10.387182] (-) TimerEvent: {}
[10.487913] (-) TimerEvent: {}
[10.588663] (-) TimerEvent: {}
[10.689536] (-) TimerEvent: {}
[10.790391] (-) TimerEvent: {}
[10.891184] (-) TimerEvent: {}
[10.991901] (-) TimerEvent: {}
[11.093033] (-) TimerEvent: {}
[11.193719] (-) TimerEvent: {}
[11.294558] (-) TimerEvent: {}
[11.395308] (-) TimerEvent: {}
[11.496215] (-) TimerEvent: {}
[11.597070] (-) TimerEvent: {}
[11.697761] (-) TimerEvent: {}
[11.798452] (-) TimerEvent: {}
[11.899154] (-) TimerEvent: {}
[11.999909] (-) TimerEvent: {}
[12.100741] (-) TimerEvent: {}
[12.201536] (-) TimerEvent: {}
[12.302244] (-) TimerEvent: {}
[12.402965] (-) TimerEvent: {}
[12.503673] (-) TimerEvent: {}
[12.604385] (-) TimerEvent: {}
[12.705134] (-) TimerEvent: {}
[12.805946] (-) TimerEvent: {}
[12.906703] (-) TimerEvent: {}
[13.007416] (-) TimerEvent: {}
[13.108145] (-) TimerEvent: {}
[13.208869] (-) TimerEvent: {}
[13.309710] (-) TimerEvent: {}
[13.410556] (-) TimerEvent: {}
[13.511365] (-) TimerEvent: {}
[13.612216] (-) TimerEvent: {}
[13.712899] (-) TimerEvent: {}
[13.813604] (-) TimerEvent: {}
[13.914644] (-) TimerEvent: {}
[14.015449] (-) TimerEvent: {}
[14.116399] (-) TimerEvent: {}
[14.217301] (-) TimerEvent: {}
[14.318065] (-) TimerEvent: {}
[14.418787] (-) TimerEvent: {}
[14.519846] (-) TimerEvent: {}
[14.621228] (-) TimerEvent: {}
[14.722046] (-) TimerEvent: {}
[14.822826] (-) TimerEvent: {}
[14.923533] (-) TimerEvent: {}
[15.024259] (-) TimerEvent: {}
[15.124955] (-) TimerEvent: {}
[15.225676] (-) TimerEvent: {}
[15.326522] (-) TimerEvent: {}
[15.427366] (-) TimerEvent: {}
[15.528186] (-) TimerEvent: {}
[15.628887] (-) TimerEvent: {}
[15.729613] (-) TimerEvent: {}
[15.830439] (-) TimerEvent: {}
[15.931171] (-) TimerEvent: {}
[15.941152] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[15.942502] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handleNode(const iterator&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[15.942850] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:210:58:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kit\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[15.943033] (octomap_server2) StderrLine: {'line': b'  210 |         virtual void handleNode(\x1b[01;35m\x1b[Kconst OcTreeT::iterator& it\x1b[m\x1b[K) {};\n'}
[15.943216] (octomap_server2) StderrLine: {'line': b'      |                                 \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~^~\x1b[m\x1b[K\n'}
[15.943348] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handleNodeInBBX(const iterator&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[15.943476] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:211:63:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kit\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[15.943599] (octomap_server2) StderrLine: {'line': b'  211 |         virtual void handleNodeInBBX(\x1b[01;35m\x1b[Kconst OcTreeT::iterator& it\x1b[m\x1b[K) {};\n'}
[15.943712] (octomap_server2) StderrLine: {'line': b'      |                                      \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~^~\x1b[m\x1b[K\n'}
[15.943850] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Koctomap_server::OctomapServer::OctomapServer(const rclcpp::NodeOptions&, std::string)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[15.943992] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:148:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Koctomap_server::OctomapServer::m_useColoredMap\x1b[m\x1b[K\xe2\x80\x99 will be initialized after [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[15.944146] (octomap_server2) StderrLine: {'line': b'  148 |         bool \x1b[01;35m\x1b[Km_useColoredMap\x1b[m\x1b[K;\n'}
[15.944289] (octomap_server2) StderrLine: {'line': b'      |              \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[15.944409] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:118:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  \xe2\x80\x98\x1b[01m\x1b[Kdouble octomap_server::OctomapServer::m_colorFactor\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[15.944558] (octomap_server2) StderrLine: {'line': b'  118 |         double \x1b[01;35m\x1b[Km_colorFactor\x1b[m\x1b[K;\n'}
[15.944673] (octomap_server2) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[15.944784] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:5:5:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  when initialized here [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[15.944924] (octomap_server2) StderrLine: {'line': b'    5 |     \x1b[01;35m\x1b[KOctomapServer\x1b[m\x1b[K::OctomapServer(\n'}
[15.945092] (octomap_server2) StderrLine: {'line': b'      |     \x1b[01;35m\x1b[K^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[16.031358] (-) TimerEvent: {}
[16.132219] (-) TimerEvent: {}
[16.204306] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[16.205219] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[16.205428] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[16.205586] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[16.205776] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[16.205938] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[16.206073] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[16.206207] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[16.206343] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[16.206477] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[16.206605] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[16.206733] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8\x1b[m\x1b[K,\n'}
[16.206858] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[16.206980] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::insertCloudCallback(const ConstSharedPtr&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.207111] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:346:50:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kformat \xe2\x80\x98\x1b[01m\x1b[K%s\x1b[m\x1b[K\xe2\x80\x99 expects argument of type \xe2\x80\x98\x1b[01m\x1b[Kchar*\x1b[m\x1b[K\xe2\x80\x99, but argument 5 has type \xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::__cxx11::basic_string<char>\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=\x07-Wformat=\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.207254] (octomap_server2) StderrLine: {'line': b'  346 |                 RCLCPP_ERROR(this->get_logger(), \x1b[01;35m\x1b[K"%s %"\x1b[m\x1b[K, msg, ex.what());\n'}
[16.207383] (octomap_server2) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[16.207532] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:346:52:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kformat string is defined here\n'}
[16.207670] (octomap_server2) StderrLine: {'line': b'  346 |                 RCLCPP_ERROR(this->get_logger(), "\x1b[01;36m\x1b[K%s\x1b[m\x1b[K %", msg, ex.what());\n'}
[16.209959] (octomap_server2) StderrLine: {'line': b'      |                                                   \x1b[01;36m\x1b[K~^\x1b[m\x1b[K\n'}
[16.210526] (octomap_server2) StderrLine: {'line': b'      |                                                    \x1b[01;36m\x1b[K|\x1b[m\x1b[K\n'}
[16.210718] (octomap_server2) StderrLine: {'line': b'      |                                                    \x1b[01;36m\x1b[Kchar*\x1b[m\x1b[K\n'}
[16.210994] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[16.211149] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[16.211282] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[16.211406] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[16.211524] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[16.211639] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[16.211758] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[16.211987] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[16.212174] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[16.212324] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[16.212451] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[16.212605] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8\x1b[m\x1b[K,\n'}
[16.212906] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[16.213124] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:346:50:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kspurious trailing \xe2\x80\x98\x1b[01m\x1b[K%\x1b[m\x1b[K\xe2\x80\x99 in format [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=\x07-Wformat=\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.213294] (octomap_server2) StderrLine: {'line': b'  346 |                 RCLCPP_ERROR(this->get_logger(), \x1b[01;35m\x1b[K"%s %"\x1b[m\x1b[K, msg, ex.what());\n'}
[16.213435] (octomap_server2) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[16.213562] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:346:54:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kformat string is defined here\n'}
[16.213712] (octomap_server2) StderrLine: {'line': b'  346 |                 RCLCPP_ERROR(this->get_logger(), "%s \x1b[01;36m\x1b[K%\x1b[m\x1b[K", msg, ex.what());\n'}
[16.213838] (octomap_server2) StderrLine: {'line': b'      |                                                      \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[16.213980] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[16.214099] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[16.214302] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[16.214459] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[16.214586] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[16.214707] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[16.214839] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[16.214959] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[16.215077] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[16.215194] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[16.215392] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[16.215539] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8\x1b[m\x1b[K,\n'}
[16.215666] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[16.215882] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:346:50:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Ktoo many arguments for format [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat-extra-args\x07-Wformat-extra-args\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.216027] (octomap_server2) StderrLine: {'line': b'  346 |                 RCLCPP_ERROR(this->get_logger(), \x1b[01;35m\x1b[K"%s %"\x1b[m\x1b[K, msg, ex.what());\n'}
[16.216148] (octomap_server2) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[16.232475] (-) TimerEvent: {}
[16.236472] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::publishAll(const rclcpp::Time&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.236989] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:600:28:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Ksize\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.237245] (octomap_server2) StderrLine: {'line': b'  600 |                     double \x1b[01;35m\x1b[Ksize\x1b[m\x1b[K = it.getSize();\n'}
[16.237457] (octomap_server2) StderrLine: {'line': b'      |                            \x1b[01;35m\x1b[K^~~~\x1b[m\x1b[K\n'}
[16.253455] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual bool octomap_server::OctomapServer::octomapBinarySrv(std::shared_ptr<octomap_msgs::srv::GetOctomap_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::GetOctomap_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.254094] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:788:52:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kreq\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.254432] (octomap_server2) StderrLine: {'line': b'  788 |         \x1b[01;35m\x1b[Kconst std::shared_ptr<OctomapSrv::Request> req\x1b[m\x1b[K,\n'}
[16.254659] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[16.254975] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual bool octomap_server::OctomapServer::octomapFullSrv(std::shared_ptr<octomap_msgs::srv::GetOctomap_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::GetOctomap_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.255197] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:807:52:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kreq\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.255456] (octomap_server2) StderrLine: {'line': b'  807 |         \x1b[01;35m\x1b[Kconst std::shared_ptr<OctomapSrv::Request> req\x1b[m\x1b[K,\n'}
[16.255671] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[16.265548] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool octomap_server::OctomapServer::clearBBXSrv(std::shared_ptr<octomap_msgs::srv::BoundingBoxQuery_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::BoundingBoxQuery_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.266206] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:822:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kresp\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.266482] (octomap_server2) StderrLine: {'line': b'  822 |         \x1b[01;35m\x1b[Kstd::shared_ptr<BBXSrv::Response> resp\x1b[m\x1b[K) {\n'}
[16.266713] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~\x1b[m\x1b[K\n'}
[16.276030] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool octomap_server::OctomapServer::resetSrv(std::shared_ptr<std_srvs::srv::Empty_Request_<std::allocator<void> > >, std::shared_ptr<std_srvs::srv::Empty_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.276572] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:860:28:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<visualization_msgs::msg::Marker_<std::allocator<void> >, std::allocator<visualization_msgs::msg::Marker_<std::allocator<void> > > >::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.276822] (octomap_server2) StderrLine: {'line': b'  860 |         for (auto i = 0; \x1b[01;35m\x1b[Ki < occupiedNodesVis.markers.size()\x1b[m\x1b[K; ++i){\n'}
[16.277143] (octomap_server2) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[16.277280] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:876:28:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<visualization_msgs::msg::Marker_<std::allocator<void> >, std::allocator<visualization_msgs::msg::Marker_<std::allocator<void> > > >::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.277431] (octomap_server2) StderrLine: {'line': b'  876 |         for (auto i = 0; \x1b[01;35m\x1b[Ki < freeNodesVis.markers.size()\x1b[m\x1b[K; ++i) {\n'}
[16.277555] (octomap_server2) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[16.278098] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:840:62:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kreq\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.278255] (octomap_server2) StderrLine: {'line': b'  840 |         \x1b[01;35m\x1b[Kconst std::shared_ptr<std_srvs::srv::Empty::Request> req\x1b[m\x1b[K,\n'}
[16.278410] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[16.278531] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:841:57:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kresp\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.278658] (octomap_server2) StderrLine: {'line': b'  841 |         \x1b[01;35m\x1b[Kstd::shared_ptr<std_srvs::srv::Empty::Response> resp\x1b[m\x1b[K) {\n'}
[16.278772] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~\x1b[m\x1b[K\n'}
[16.332633] (-) TimerEvent: {}
[16.338976] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handlePreNodeTraversal(const rclcpp::Time&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.339569] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:1168:59:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kunsigned int\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.339936] (octomap_server2) StderrLine: {'line': b' 1168 |                 for (unsigned int j = mapUpdateBBXMinY; \x1b[01;35m\x1b[Kj <= mapUpdateBBXMaxY\x1b[m\x1b[K; ++j) {\n'}
[16.340152] (octomap_server2) StderrLine: {'line': b'      |                                                         \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[16.343565] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handlePostNodeTraversal(const rclcpp::Time&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.344245] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:1178:29:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Krostime\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.344447] (octomap_server2) StderrLine: {'line': b' 1178 |         \x1b[01;35m\x1b[Kconst rclcpp::Time& rostime\x1b[m\x1b[K){\n'}
[16.344604] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~^~~~~~~\x1b[m\x1b[K\n'}
[16.432815] (-) TimerEvent: {}
[16.533529] (-) TimerEvent: {}
[16.634252] (-) TimerEvent: {}
[16.735018] (-) TimerEvent: {}
[16.835854] (-) TimerEvent: {}
[16.936564] (-) TimerEvent: {}
[17.037672] (-) TimerEvent: {}
[17.138413] (-) TimerEvent: {}
[17.239449] (-) TimerEvent: {}
[17.340212] (-) TimerEvent: {}
[17.441154] (-) TimerEvent: {}
[17.541877] (-) TimerEvent: {}
[17.642635] (-) TimerEvent: {}
[17.743350] (-) TimerEvent: {}
[17.844105] (-) TimerEvent: {}
[17.944847] (-) TimerEvent: {}
[18.045802] (-) TimerEvent: {}
[18.146632] (-) TimerEvent: {}
[18.247362] (-) TimerEvent: {}
[18.348137] (-) TimerEvent: {}
[18.449130] (-) TimerEvent: {}
[18.549872] (-) TimerEvent: {}
[18.651033] (-) TimerEvent: {}
[18.751818] (-) TimerEvent: {}
[18.852543] (-) TimerEvent: {}
[18.953291] (-) TimerEvent: {}
[19.054030] (-) TimerEvent: {}
[19.155338] (-) TimerEvent: {}
[19.257370] (-) TimerEvent: {}
[19.358283] (-) TimerEvent: {}
[19.459168] (-) TimerEvent: {}
[19.559974] (-) TimerEvent: {}
[19.661164] (-) TimerEvent: {}
[19.761954] (-) TimerEvent: {}
[19.862827] (-) TimerEvent: {}
[19.963645] (-) TimerEvent: {}
[20.064591] (-) TimerEvent: {}
[20.165359] (-) TimerEvent: {}
[20.266225] (-) TimerEvent: {}
[20.367132] (-) TimerEvent: {}
[20.467946] (-) TimerEvent: {}
[20.568679] (-) TimerEvent: {}
[20.669435] (-) TimerEvent: {}
[20.770156] (-) TimerEvent: {}
[20.870850] (-) TimerEvent: {}
[20.971547] (-) TimerEvent: {}
[21.072393] (-) TimerEvent: {}
[21.173080] (-) TimerEvent: {}
[21.273893] (-) TimerEvent: {}
[21.374572] (-) TimerEvent: {}
[21.475228] (-) TimerEvent: {}
[21.575948] (-) TimerEvent: {}
[21.676929] (-) TimerEvent: {}
[21.777788] (-) TimerEvent: {}
[21.878554] (-) TimerEvent: {}
[21.979294] (-) TimerEvent: {}
[22.080125] (-) TimerEvent: {}
[22.180812] (-) TimerEvent: {}
[22.281517] (-) TimerEvent: {}
[22.382351] (-) TimerEvent: {}
[22.483141] (-) TimerEvent: {}
[22.583879] (-) TimerEvent: {}
[22.684637] (-) TimerEvent: {}
[22.785500] (-) TimerEvent: {}
[22.886232] (-) TimerEvent: {}
[22.986909] (-) TimerEvent: {}
[23.087609] (-) TimerEvent: {}
[23.188452] (-) TimerEvent: {}
[23.289143] (-) TimerEvent: {}
[23.389839] (-) TimerEvent: {}
[23.490548] (-) TimerEvent: {}
[23.591233] (-) TimerEvent: {}
[23.691941] (-) TimerEvent: {}
[23.792627] (-) TimerEvent: {}
[23.893302] (-) TimerEvent: {}
[23.993982] (-) TimerEvent: {}
[24.094678] (-) TimerEvent: {}
[24.195351] (-) TimerEvent: {}
[24.296041] (-) TimerEvent: {}
[24.396712] (-) TimerEvent: {}
[24.497408] (-) TimerEvent: {}
[24.598107] (-) TimerEvent: {}
[24.698788] (-) TimerEvent: {}
[24.799462] (-) TimerEvent: {}
[24.900128] (-) TimerEvent: {}
[25.000816] (-) TimerEvent: {}
[25.101496] (-) TimerEvent: {}
[25.202159] (-) TimerEvent: {}
[25.302859] (-) TimerEvent: {}
[25.403505] (-) TimerEvent: {}
[25.504217] (-) TimerEvent: {}
[25.604890] (-) TimerEvent: {}
[25.705607] (-) TimerEvent: {}
[25.806269] (-) TimerEvent: {}
[25.907321] (-) TimerEvent: {}
[26.008049] (-) TimerEvent: {}
[26.108766] (-) TimerEvent: {}
[26.209459] (-) TimerEvent: {}
[26.310147] (-) TimerEvent: {}
[26.410852] (-) TimerEvent: {}
[26.511558] (-) TimerEvent: {}
[26.612278] (-) TimerEvent: {}
[26.712995] (-) TimerEvent: {}
[26.813689] (-) TimerEvent: {}
[26.914392] (-) TimerEvent: {}
[27.015098] (-) TimerEvent: {}
[27.115820] (-) TimerEvent: {}
[27.216508] (-) TimerEvent: {}
[27.317229] (-) TimerEvent: {}
[27.417903] (-) TimerEvent: {}
[27.518569] (-) TimerEvent: {}
[27.623442] (-) TimerEvent: {}
[27.724126] (-) TimerEvent: {}
[27.824807] (-) TimerEvent: {}
[27.925471] (-) TimerEvent: {}
[28.026157] (-) TimerEvent: {}
[28.126832] (-) TimerEvent: {}
[28.227541] (-) TimerEvent: {}
[28.328234] (-) TimerEvent: {}
[28.428929] (-) TimerEvent: {}
[28.529614] (-) TimerEvent: {}
[28.630286] (-) TimerEvent: {}
[28.730957] (-) TimerEvent: {}
[28.831621] (-) TimerEvent: {}
[28.932307] (-) TimerEvent: {}
[29.032974] (-) TimerEvent: {}
[29.133662] (-) TimerEvent: {}
[29.234330] (-) TimerEvent: {}
[29.335003] (-) TimerEvent: {}
[29.435663] (-) TimerEvent: {}
[29.536361] (-) TimerEvent: {}
[29.637036] (-) TimerEvent: {}
[29.737710] (-) TimerEvent: {}
[29.838388] (-) TimerEvent: {}
[29.939082] (-) TimerEvent: {}
[30.039750] (-) TimerEvent: {}
[30.140454] (-) TimerEvent: {}
[30.241123] (-) TimerEvent: {}
[30.341826] (-) TimerEvent: {}
[30.442523] (-) TimerEvent: {}
[30.543247] (-) TimerEvent: {}
[30.643948] (-) TimerEvent: {}
[30.744662] (-) TimerEvent: {}
[30.845375] (-) TimerEvent: {}
[30.946068] (-) TimerEvent: {}
[31.046752] (-) TimerEvent: {}
[31.147446] (-) TimerEvent: {}
[31.248173] (-) TimerEvent: {}
[31.348858] (-) TimerEvent: {}
[31.449548] (-) TimerEvent: {}
[31.550230] (-) TimerEvent: {}
[31.650959] (-) TimerEvent: {}
[31.751708] (-) TimerEvent: {}
[31.852461] (-) TimerEvent: {}
[31.953254] (-) TimerEvent: {}
[32.054031] (-) TimerEvent: {}
[32.154720] (-) TimerEvent: {}
[32.255408] (-) TimerEvent: {}
[32.356115] (-) TimerEvent: {}
[32.456792] (-) TimerEvent: {}
[32.557464] (-) TimerEvent: {}
[32.658150] (-) TimerEvent: {}
[32.758819] (-) TimerEvent: {}
[32.859505] (-) TimerEvent: {}
[32.960219] (-) TimerEvent: {}
[33.060913] (-) TimerEvent: {}
[33.161720] (-) TimerEvent: {}
[33.262520] (-) TimerEvent: {}
[33.363237] (-) TimerEvent: {}
[33.464125] (-) TimerEvent: {}
[33.565003] (-) TimerEvent: {}
[33.666000] (-) TimerEvent: {}
[33.766889] (-) TimerEvent: {}
[33.867712] (-) TimerEvent: {}
[33.968438] (-) TimerEvent: {}
[34.069462] (-) TimerEvent: {}
[34.170320] (-) TimerEvent: {}
[34.271184] (-) TimerEvent: {}
[34.371960] (-) TimerEvent: {}
[34.472789] (-) TimerEvent: {}
[34.573621] (-) TimerEvent: {}
[34.674575] (-) TimerEvent: {}
[34.775753] (-) TimerEvent: {}
[34.876514] (-) TimerEvent: {}
[34.977380] (-) TimerEvent: {}
[35.078240] (-) TimerEvent: {}
[35.179079] (-) TimerEvent: {}
[35.279745] (-) TimerEvent: {}
[35.380458] (-) TimerEvent: {}
[35.481136] (-) TimerEvent: {}
[35.581843] (-) TimerEvent: {}
[35.682589] (-) TimerEvent: {}
[35.783435] (-) TimerEvent: {}
[35.884268] (-) TimerEvent: {}
[35.985067] (-) TimerEvent: {}
[36.085858] (-) TimerEvent: {}
[36.186548] (-) TimerEvent: {}
[36.287558] (-) TimerEvent: {}
[36.388410] (-) TimerEvent: {}
[36.489238] (-) TimerEvent: {}
[36.589949] (-) TimerEvent: {}
[36.690765] (-) TimerEvent: {}
[36.791518] (-) TimerEvent: {}
[36.892248] (-) TimerEvent: {}
[36.993077] (-) TimerEvent: {}
[37.093901] (-) TimerEvent: {}
[37.194599] (-) TimerEvent: {}
[37.295347] (-) TimerEvent: {}
[37.396418] (-) TimerEvent: {}
[37.497138] (-) TimerEvent: {}
[37.597938] (-) TimerEvent: {}
[37.698738] (-) TimerEvent: {}
[37.799420] (-) TimerEvent: {}
[37.900246] (-) TimerEvent: {}
[38.001089] (-) TimerEvent: {}
[38.101907] (-) TimerEvent: {}
[38.202686] (-) TimerEvent: {}
[38.303392] (-) TimerEvent: {}
[38.404226] (-) TimerEvent: {}
[38.505053] (-) TimerEvent: {}
[38.605864] (-) TimerEvent: {}
[38.706624] (-) TimerEvent: {}
[38.807448] (-) TimerEvent: {}
[38.908202] (-) TimerEvent: {}
[39.008875] (-) TimerEvent: {}
[39.109550] (-) TimerEvent: {}
[39.210232] (-) TimerEvent: {}
[39.310885] (-) TimerEvent: {}
[39.411559] (-) TimerEvent: {}
[39.512251] (-) TimerEvent: {}
[39.612914] (-) TimerEvent: {}
[39.713566] (-) TimerEvent: {}
[39.814214] (-) TimerEvent: {}
[39.914873] (-) TimerEvent: {}
[40.015549] (-) TimerEvent: {}
[40.116247] (-) TimerEvent: {}
[40.216905] (-) TimerEvent: {}
[40.317881] (-) TimerEvent: {}
[40.418554] (-) TimerEvent: {}
[40.519438] (-) TimerEvent: {}
[40.620193] (-) TimerEvent: {}
[40.720870] (-) TimerEvent: {}
[40.821568] (-) TimerEvent: {}
[40.922253] (-) TimerEvent: {}
[41.022945] (-) TimerEvent: {}
[41.123639] (-) TimerEvent: {}
[41.224352] (-) TimerEvent: {}
[41.325019] (-) TimerEvent: {}
[41.425676] (-) TimerEvent: {}
[41.526369] (-) TimerEvent: {}
[41.627020] (-) TimerEvent: {}
[41.727685] (-) TimerEvent: {}
[41.828404] (-) TimerEvent: {}
[41.929067] (-) TimerEvent: {}
[42.029741] (-) TimerEvent: {}
[42.130412] (-) TimerEvent: {}
[42.231109] (-) TimerEvent: {}
[42.331844] (-) TimerEvent: {}
[42.432550] (-) TimerEvent: {}
[42.533242] (-) TimerEvent: {}
[42.633947] (-) TimerEvent: {}
[42.734634] (-) TimerEvent: {}
[42.835307] (-) TimerEvent: {}
[42.935964] (-) TimerEvent: {}
[43.036652] (-) TimerEvent: {}
[43.137339] (-) TimerEvent: {}
[43.238006] (-) TimerEvent: {}
[43.338664] (-) TimerEvent: {}
[43.439366] (-) TimerEvent: {}
[43.540068] (-) TimerEvent: {}
[43.640749] (-) TimerEvent: {}
[43.741447] (-) TimerEvent: {}
[43.842118] (-) TimerEvent: {}
[43.942797] (-) TimerEvent: {}
[44.043512] (-) TimerEvent: {}
[44.144720] (-) TimerEvent: {}
[44.245415] (-) TimerEvent: {}
[44.346095] (-) TimerEvent: {}
[44.446806] (-) TimerEvent: {}
[44.547915] (-) TimerEvent: {}
[44.648733] (-) TimerEvent: {}
[44.749448] (-) TimerEvent: {}
[44.850171] (-) TimerEvent: {}
[44.950874] (-) TimerEvent: {}
[45.051631] (-) TimerEvent: {}
[45.152378] (-) TimerEvent: {}
[45.253283] (-) TimerEvent: {}
[45.353993] (-) TimerEvent: {}
[45.454650] (-) TimerEvent: {}
[45.555462] (-) TimerEvent: {}
[45.656189] (-) TimerEvent: {}
[45.756956] (-) TimerEvent: {}
[45.857642] (-) TimerEvent: {}
[45.958358] (-) TimerEvent: {}
[46.059061] (-) TimerEvent: {}
[46.159754] (-) TimerEvent: {}
[46.260492] (-) TimerEvent: {}
[46.361209] (-) TimerEvent: {}
[46.461920] (-) TimerEvent: {}
[46.562607] (-) TimerEvent: {}
[46.663299] (-) TimerEvent: {}
[46.764038] (-) TimerEvent: {}
[46.864746] (-) TimerEvent: {}
[46.965489] (-) TimerEvent: {}
[47.066169] (-) TimerEvent: {}
[47.166888] (-) TimerEvent: {}
[47.267584] (-) TimerEvent: {}
[47.368324] (-) TimerEvent: {}
[47.469047] (-) TimerEvent: {}
[47.569742] (-) TimerEvent: {}
[47.670437] (-) TimerEvent: {}
[47.771142] (-) TimerEvent: {}
[47.871858] (-) TimerEvent: {}
[47.972539] (-) TimerEvent: {}
[48.073263] (-) TimerEvent: {}
[48.173970] (-) TimerEvent: {}
[48.274663] (-) TimerEvent: {}
[48.375367] (-) TimerEvent: {}
[48.476138] (-) TimerEvent: {}
[48.576862] (-) TimerEvent: {}
[48.677535] (-) TimerEvent: {}
[48.778230] (-) TimerEvent: {}
[48.878936] (-) TimerEvent: {}
[48.979633] (-) TimerEvent: {}
[49.080394] (-) TimerEvent: {}
[49.181206] (-) TimerEvent: {}
[49.282053] (-) TimerEvent: {}
[49.382867] (-) TimerEvent: {}
[49.483605] (-) TimerEvent: {}
[49.584395] (-) TimerEvent: {}
[49.685186] (-) TimerEvent: {}
[49.785885] (-) TimerEvent: {}
[49.886662] (-) TimerEvent: {}
[49.987497] (-) TimerEvent: {}
[50.088195] (-) TimerEvent: {}
[50.189127] (-) TimerEvent: {}
[50.290055] (-) TimerEvent: {}
[50.390745] (-) TimerEvent: {}
[50.491436] (-) TimerEvent: {}
[50.592207] (-) TimerEvent: {}
[50.692869] (-) TimerEvent: {}
[50.793554] (-) TimerEvent: {}
[50.894218] (-) TimerEvent: {}
[50.994864] (-) TimerEvent: {}
[51.095545] (-) TimerEvent: {}
[51.196240] (-) TimerEvent: {}
[51.297009] (-) TimerEvent: {}
[51.397682] (-) TimerEvent: {}
[51.498343] (-) TimerEvent: {}
[51.599001] (-) TimerEvent: {}
[51.699692] (-) TimerEvent: {}
[51.800384] (-) TimerEvent: {}
[51.901098] (-) TimerEvent: {}
[52.001804] (-) TimerEvent: {}
[52.102498] (-) TimerEvent: {}
[52.203181] (-) TimerEvent: {}
[52.303905] (-) TimerEvent: {}
[52.404597] (-) TimerEvent: {}
[52.505274] (-) TimerEvent: {}
[52.606258] (-) TimerEvent: {}
[52.707031] (-) TimerEvent: {}
[52.807697] (-) TimerEvent: {}
[52.908438] (-) TimerEvent: {}
[52.931264] (octomap_server2) StdoutLine: {'line': b'[ 66%] \x1b[32m\x1b[1mLinking CXX shared library liboctomap_server2.so\x1b[0m\n'}
[53.008625] (-) TimerEvent: {}
[53.109443] (-) TimerEvent: {}
[53.210242] (-) TimerEvent: {}
[53.311054] (-) TimerEvent: {}
[53.411924] (-) TimerEvent: {}
[53.512826] (-) TimerEvent: {}
[53.613587] (-) TimerEvent: {}
[53.714422] (-) TimerEvent: {}
[53.815225] (-) TimerEvent: {}
[53.916095] (-) TimerEvent: {}
[54.016909] (-) TimerEvent: {}
[54.117695] (-) TimerEvent: {}
[54.218460] (-) TimerEvent: {}
[54.319304] (-) TimerEvent: {}
[54.420106] (-) TimerEvent: {}
[54.520812] (-) TimerEvent: {}
[54.621542] (-) TimerEvent: {}
[54.722332] (-) TimerEvent: {}
[54.823017] (-) TimerEvent: {}
[54.923745] (-) TimerEvent: {}
[55.024450] (-) TimerEvent: {}
[55.125183] (-) TimerEvent: {}
[55.225864] (-) TimerEvent: {}
[55.326568] (-) TimerEvent: {}
[55.427238] (-) TimerEvent: {}
[55.527949] (-) TimerEvent: {}
[55.628617] (-) TimerEvent: {}
[55.729315] (-) TimerEvent: {}
[55.830047] (-) TimerEvent: {}
[55.930727] (-) TimerEvent: {}
[56.031420] (-) TimerEvent: {}
[56.132175] (-) TimerEvent: {}
[56.232867] (-) TimerEvent: {}
[56.333563] (-) TimerEvent: {}
[56.434442] (-) TimerEvent: {}
[56.535208] (-) TimerEvent: {}
[56.635933] (-) TimerEvent: {}
[56.736747] (-) TimerEvent: {}
[56.837448] (-) TimerEvent: {}
[56.938195] (-) TimerEvent: {}
[57.038892] (-) TimerEvent: {}
[57.141297] (-) TimerEvent: {}
[57.242001] (-) TimerEvent: {}
[57.342746] (-) TimerEvent: {}
[57.443423] (-) TimerEvent: {}
[57.544115] (-) TimerEvent: {}
[57.644792] (-) TimerEvent: {}
[57.745476] (-) TimerEvent: {}
[57.846186] (-) TimerEvent: {}
[57.946851] (-) TimerEvent: {}
[58.047543] (-) TimerEvent: {}
[58.148281] (-) TimerEvent: {}
[58.248951] (-) TimerEvent: {}
[58.349637] (-) TimerEvent: {}
[58.450269] (-) TimerEvent: {}
[58.550956] (-) TimerEvent: {}
[58.635472] (octomap_server2) StdoutLine: {'line': b'[100%] Built target octomap_server2\n'}
[58.652172] (octomap_server2) CommandEnded: {'returncode': 0}
[58.653933] (-) TimerEvent: {}
[58.655613] (octomap_server2) JobProgress: {'identifier': 'octomap_server2', 'progress': 'install'}
[58.670968] (octomap_server2) Command: {'cmd': ['/usr/local/bin/cmake', '--install', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'], 'cwd': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'nvidia'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('JETSON_L4T', '36.3.0'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin NX Engineering Reference Developer Kit'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1734'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'nvidia'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (8GB ram)'), ('JETSON_SERIAL_NUMBER', '1421224256350'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'nvidia'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/nvidia-desktop:@/tmp/.ICE-unix/1734,unix/nvidia-desktop:/tmp/.ICE-unix/1734'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ba536020_4df3_40ed_88e6_2aa3b1bbae09'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('GNOME_TERMINAL_SERVICE', ':1.126'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.0'), ('CMAKE_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0003')]), 'shell': False}
[58.685097] (octomap_server2) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[58.686530] (octomap_server2) StdoutLine: {'line': b'-- Execute custom install script\n'}
[58.688508] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server\n'}
[58.689386] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so\n'}
[58.692225] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py\n'}
[58.692811] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2\n'}
[58.693355] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2\n'}
[58.694090] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh\n'}
[58.694603] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv\n'}
[58.695224] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh\n'}
[58.695593] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv\n'}
[58.696168] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash\n'}
[58.696656] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh\n'}
[58.697297] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh\n'}
[58.697985] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv\n'}
[58.698492] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv\n'}
[58.707661] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2\n'}
[58.708344] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2\n'}
[58.708898] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake\n'}
[58.709361] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake\n'}
[58.709848] (octomap_server2) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml\n'}
[58.714030] (octomap_server2) CommandEnded: {'returncode': 0}
[58.754113] (-) TimerEvent: {}
[58.804880] (octomap_server2) JobEnded: {'identifier': 'octomap_server2', 'rc': 0}
[58.807919] (-) EventReactorShutdown: {}
