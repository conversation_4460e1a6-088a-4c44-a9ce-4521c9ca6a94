#pragma once
#include "commons.h"
#include <filesystem>
#include <pcl/io/pcd_io.h>
#include <pcl/registration/icp.h>
#include <pcl/filters/voxel_grid.h>
#include <grid_map_core/grid_map_core.hpp>
#include <chrono>

struct ICPConfig
{
    double refine_scan_resolution = 0.1;
    double refine_map_resolution = 0.1;
    double refine_score_thresh = 0.1;
    int refine_max_iteration = 10;

    double rough_scan_resolution = 0.25;
    double rough_map_resolution = 0.25;
    double rough_score_thresh = 0.2;
    int rough_max_iteration = 5;
    
    // 自动全局重定位参数
    bool auto_global_relocalization_enable = true;
    int auto_global_max_iterations = 1;
    double auto_global_score_threshold = 100.0;
    std::vector<double> auto_global_search_range_x = {-10.0, 10.0};
    std::vector<double> auto_global_search_range_y = {-10.0, 10.0};
    std::vector<double> auto_global_search_range_yaw = {-3.14, 3.14};
    double auto_global_resolution = 0.5;
    double auto_global_angle_resolution = 0.1;
    
    // 自动触发重定位参数
    bool auto_relocalization_enable = true;
    double auto_relocalization_distance_threshold = 10.0;
    double auto_relocalization_time_threshold = 60.0;
    double auto_relocalization_fitness_score_threshold = 0.2;
};

class ICPLocalizer
{
public:
    ICPLocalizer(const ICPConfig &config);
    
    bool loadMap(const std::string &path);
    
    void setInput(const CloudType::Ptr &cloud);

    bool align(M4F &guess);
    
    // 自动全局重定位
    bool autoGlobalRelocalization(M4F &guess);
    
    // 自动触发重定位检测
    bool checkNeedRelocalization(const M4F &current_pose, const std::chrono::steady_clock::time_point &current_time);
    
    // 将点云变换到2D格式用于评分
    DiscreteTransformation::Points convertCloudTo2DPoints(const CloudType::Ptr &cloud, bool is_ground);
    
    // 创建网格地图金字塔
    void createGridMapPyramid(int max_level);
    
    // 计算变换的评分
    float calculateScore(const DiscreteTransformation &trans, 
                          const DiscreteTransformation::Points &ground_points,
                          const DiscreteTransformation::Points &wall_points);
    
    ICPConfig &config() { return m_config; }
    CloudType::Ptr roughMap() { return m_rough_tgt; }
    CloudType::Ptr refineMap() { return m_refine_tgt; }

private:
    ICPConfig m_config;
    pcl::VoxelGrid<PointType> m_voxel_filter;
    pcl::IterativeClosestPoint<PointType, PointType> m_refine_icp;
    pcl::IterativeClosestPoint<PointType, PointType> m_rough_icp;
    CloudType::Ptr m_refine_inp;
    CloudType::Ptr m_rough_inp;
    CloudType::Ptr m_refine_tgt;
    CloudType::Ptr m_rough_tgt;
    std::string m_pcd_path;
    
    // 用于自动重定位的变量
    bool m_auto_global_success = false;
    std::chrono::steady_clock::time_point m_last_relocalization_time;
    M4F m_last_relocalization_pose = M4F::Identity();
    
    // 用于自动全局重定位的网格地图
    static const int MAX_PYRAMID_LEVEL = 4;
    std::vector<std::string> m_pyramid_level_names = {"level0", "level1", "level2", "level3"};
    grid_map::GridMap m_grid_map_pyramid;
};