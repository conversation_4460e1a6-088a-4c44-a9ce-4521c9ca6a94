[0.225s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[0.225s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0xffff90c65450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff90c648b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff90c648b0>>)
[0.570s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.571s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.571s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.571s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.571s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.571s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.571s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/fastlio2/octomap_ros2'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.602s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'octomap_server2'
[0.602s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.602s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.602s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.602s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.602s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.642s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.642s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.645s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/fastlio2/ws_livox/install
[0.648s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 367 installed packages in /opt/ros/humble
[0.681s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.777s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_args' from command line to 'None'
[0.777s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target' from command line to 'None'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_cache' from command line to 'False'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_clean_first' from command line to 'False'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'cmake_force_configure' from command line to 'False'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'ament_cmake_args' from command line to 'None'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_cmake_args' from command line to 'None'
[0.778s] Level 5:colcon.colcon_core.verb:set package 'octomap_server2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.778s] DEBUG:colcon.colcon_core.verb:Building package 'octomap_server2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2', 'merge_install': False, 'path': '/home/<USER>/fastlio2/octomap_ros2', 'symlink_install': True, 'test_result_base': None}
[0.778s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.781s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.781s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/fastlio2/octomap_ros2' with build type 'ament_cmake'
[0.782s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/fastlio2/octomap_ros2'
[0.786s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.786s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.787s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.803s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[9.877s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[9.885s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[67.585s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[67.614s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[67.741s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[67.747s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[67.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[67.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[67.766s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[67.768s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[67.775s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[67.778s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[67.782s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[67.782s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[67.783s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[67.785s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[67.786s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[67.787s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[67.787s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[67.789s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[67.790s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[67.791s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[67.794s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[67.797s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[67.800s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[67.803s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[67.806s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[67.810s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(octomap_server2)
[67.811s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake module files
[67.813s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2' for CMake config files
[67.813s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'cmake_prefix_path')
[67.814s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.ps1'
[67.815s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.dsv'
[67.816s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/cmake_prefix_path.sh'
[67.817s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib'
[67.818s] Level 1:colcon.colcon_core.shell:create_environment_hook('octomap_server2', 'ld_library_path_lib')
[67.818s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.ps1'
[67.819s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.dsv'
[67.820s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/hook/ld_library_path_lib.sh'
[67.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[67.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/pkgconfig/octomap_server2.pc'
[67.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/python3.10/site-packages'
[67.822s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/bin'
[67.823s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.ps1'
[67.824s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv'
[67.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.sh'
[67.827s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.bash'
[67.828s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.zsh'
[67.829s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/colcon-core/packages/octomap_server2)
[67.830s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[67.832s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[67.832s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[67.833s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[67.855s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[67.855s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[67.855s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[67.904s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[67.905s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.ps1'
[67.908s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_ps1.py'
[67.914s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.ps1'
[67.918s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.sh'
[67.920s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/fastlio2/octomap_ros2/install/_local_setup_util_sh.py'
[67.922s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.sh'
[67.925s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.bash'
[67.926s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.bash'
[67.929s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/fastlio2/octomap_ros2/install/local_setup.zsh'
[67.930s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/fastlio2/octomap_ros2/install/setup.zsh'
