{"artifacts": [{"path": "octomap_server"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "rclcpp_components_register_node", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "include_directories", "target_include_directories"], "files": ["/opt/ros/humble/share/rclcpp_components/cmake/rclcpp_components_register_node.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/rclcpp_componentsConfig.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/export_rclcpp_componentsExport.cmake", "/opt/ros/humble/share/rclcpp_components/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/rcl_interfaces/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_interfaces/cmake/rcl_interfacesConfig.cmake", "/opt/ros/humble/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/humble/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_generator_pyExport.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 71, "parent": 0}, {"command": 0, "file": 0, "line": 72, "parent": 1}, {"command": 3, "file": 0, "line": 73, "parent": 1}, {"command": 2, "file": 2, "line": 145, "parent": 3}, {"command": 6, "file": 1, "line": 19, "parent": 0}, {"file": 5, "parent": 5}, {"command": 5, "file": 5, "line": 41, "parent": 6}, {"file": 4, "parent": 7}, {"command": 5, "file": 4, "line": 9, "parent": 8}, {"file": 3, "parent": 9}, {"command": 4, "file": 3, "line": 56, "parent": 10}, {"command": 5, "file": 5, "line": 41, "parent": 6}, {"file": 11, "parent": 12}, {"command": 6, "file": 11, "line": 21, "parent": 13}, {"file": 10, "parent": 14}, {"command": 5, "file": 10, "line": 41, "parent": 15}, {"file": 9, "parent": 16}, {"command": 6, "file": 9, "line": 21, "parent": 17}, {"file": 8, "parent": 18}, {"command": 5, "file": 8, "line": 41, "parent": 19}, {"file": 7, "parent": 20}, {"command": 5, "file": 7, "line": 9, "parent": 21}, {"file": 6, "parent": 22}, {"command": 4, "file": 6, "line": 56, "parent": 23}, {"command": 5, "file": 8, "line": 41, "parent": 19}, {"file": 15, "parent": 25}, {"command": 6, "file": 15, "line": 21, "parent": 26}, {"file": 14, "parent": 27}, {"command": 5, "file": 14, "line": 41, "parent": 28}, {"file": 13, "parent": 29}, {"command": 5, "file": 13, "line": 9, "parent": 30}, {"file": 12, "parent": 31}, {"command": 4, "file": 12, "line": 56, "parent": 32}, {"command": 6, "file": 1, "line": 20, "parent": 0}, {"file": 20, "parent": 34}, {"command": 5, "file": 20, "line": 41, "parent": 35}, {"file": 19, "parent": 36}, {"command": 6, "file": 19, "line": 21, "parent": 37}, {"file": 18, "parent": 38}, {"command": 5, "file": 18, "line": 41, "parent": 39}, {"file": 17, "parent": 40}, {"command": 5, "file": 17, "line": 9, "parent": 41}, {"file": 16, "parent": 42}, {"command": 4, "file": 16, "line": 56, "parent": 43}, {"command": 5, "file": 20, "line": 41, "parent": 35}, {"file": 22, "parent": 45}, {"command": 5, "file": 22, "line": 9, "parent": 46}, {"file": 21, "parent": 47}, {"command": 4, "file": 21, "line": 64, "parent": 48}, {"command": 6, "file": 15, "line": 21, "parent": 26}, {"file": 25, "parent": 50}, {"command": 5, "file": 25, "line": 41, "parent": 51}, {"file": 24, "parent": 52}, {"command": 5, "file": 24, "line": 9, "parent": 53}, {"file": 23, "parent": 54}, {"command": 4, "file": 23, "line": 56, "parent": 55}, {"command": 5, "file": 24, "line": 9, "parent": 53}, {"file": 26, "parent": 57}, {"command": 4, "file": 26, "line": 56, "parent": 58}, {"command": 5, "file": 24, "line": 9, "parent": 53}, {"file": 27, "parent": 60}, {"command": 4, "file": 27, "line": 56, "parent": 61}, {"command": 6, "file": 9, "line": 21, "parent": 17}, {"file": 32, "parent": 63}, {"command": 5, "file": 32, "line": 41, "parent": 64}, {"file": 31, "parent": 65}, {"command": 6, "file": 31, "line": 21, "parent": 66}, {"file": 30, "parent": 67}, {"command": 5, "file": 30, "line": 41, "parent": 68}, {"file": 29, "parent": 69}, {"command": 5, "file": 29, "line": 9, "parent": 70}, {"file": 28, "parent": 71}, {"command": 4, "file": 28, "line": 56, "parent": 72}, {"command": 6, "file": 31, "line": 21, "parent": 66}, {"file": 37, "parent": 74}, {"command": 5, "file": 37, "line": 41, "parent": 75}, {"file": 36, "parent": 76}, {"command": 6, "file": 36, "line": 21, "parent": 77}, {"file": 35, "parent": 78}, {"command": 5, "file": 35, "line": 41, "parent": 79}, {"file": 34, "parent": 80}, {"command": 5, "file": 34, "line": 9, "parent": 81}, {"file": 33, "parent": 82}, {"command": 4, "file": 33, "line": 56, "parent": 83}, {"command": 5, "file": 24, "line": 9, "parent": 53}, {"file": 38, "parent": 85}, {"command": 4, "file": 38, "line": 56, "parent": 86}, {"command": 7, "file": 1, "line": 15, "parent": 0}, {"command": 8, "file": 1, "line": 40, "parent": 0}, {"command": 8, "file": 1, "line": 41, "parent": 0}, {"command": 9, "file": 2, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17"}, {"backtrace": 88, "fragment": "-Wall"}, {"backtrace": 88, "fragment": "-Wextra"}, {"backtrace": 88, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 89, "path": "/usr/include/pcl-1.12"}, {"backtrace": 89, "path": "/usr/include/eigen3"}, {"backtrace": 89, "path": "/usr/include/ni"}, {"backtrace": 89, "path": "/usr/include/openni2"}, {"backtrace": 90, "path": "/home/<USER>/fastlio2/octomap_ros2/include"}, {"backtrace": 91, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 91, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 91, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_components"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/composition_interfaces"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [0]}], "id": "octomap_server::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 56, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 56, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 56, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 59, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 84, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 87, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "octomap_server", "nameOnDisk": "octomap_server", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "isGenerated": true, "path": "build/octomap_server2/rclcpp_components/node_main_octomap_server.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}