[0.028s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[0.068s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.564s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.601s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.710s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.719s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.741s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.772s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.811s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.924s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.930s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.197s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.349s] -- Found rclcpp_components: 16.0.12 (/opt/ros/humble/share/rclcpp_components/cmake)
[1.422s] [33mCMake Warning (dev) at /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:146 (find_package):
[1.422s]   Policy CMP0144 is not set: find_package uses upper-case <PACKAGENAME>_ROOT
[1.422s]   variables.  Run "cmake --help-policy CMP0144" for policy details.  Use the
[1.422s]   cmake_policy command to set the policy and suppress this warning.
[1.423s] 
[1.423s]   CMake variable EIGEN_ROOT is set to:
[1.423s] 
[1.423s]     /usr/include/eigen3
[1.424s] 
[1.424s]   For compatibility, find_package is ignoring the variable, but code in a
[1.424s]   .cmake module might still use it.
[1.424s] Call Stack (most recent call first):
[1.424s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:299 (find_eigen)
[1.425s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:543 (find_external_library)
[1.425s]   CMakeLists.txt:21 (find_package)
[1.425s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.425s] [0m
[1.436s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.470s] [33mCMake Warning (dev) at /usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindFLANN.cmake:44 (find_package):
[1.470s]   Policy CMP0144 is not set: find_package uses upper-case <PACKAGENAME>_ROOT
[1.470s]   variables.  Run "cmake --help-policy CMP0144" for policy details.  Use the
[1.470s]   cmake_policy command to set the policy and suppress this warning.
[1.470s] 
[1.471s]   CMake variable FLANN_ROOT is set to:
[1.471s] 
[1.471s]     /usr
[1.471s] 
[1.471s]   For compatibility, find_package is ignoring the variable, but code in a
[1.471s]   .cmake module might still use it.
[1.471s] Call Stack (most recent call first):
[1.471s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:256 (find_package)
[1.471s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:301 (find_flann)
[1.472s]   /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:543 (find_external_library)
[1.472s]   CMakeLists.txt:21 (find_package)
[1.472s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.472s] [0m
[1.513s] -- FLANN found (include: /usr/include, lib: /usr/lib/aarch64-linux-gnu/libflann_cpp.so)
[2.083s] -- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") 
[2.640s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.658s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
[2.658s] [0m** WARNING ** io features related to pcap will be disabled[0m
[3.216s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[3.235s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.250s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.803s] -- Found Qhull version 8.0.2
[4.361s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[4.936s] -- looking for PCL_COMMON
[4.937s] -- looking for PCL_KDTREE
[4.938s] -- looking for PCL_OCTREE
[4.939s] -- looking for PCL_SEARCH
[4.940s] -- looking for PCL_SAMPLE_CONSENSUS
[4.941s] -- looking for PCL_FILTERS
[4.942s] -- looking for PCL_2D
[4.942s] -- looking for PCL_GEOMETRY
[4.943s] -- looking for PCL_IO
[4.944s] -- looking for PCL_FEATURES
[4.945s] -- looking for PCL_ML
[4.946s] -- looking for PCL_SEGMENTATION
[4.947s] -- looking for PCL_VISUALIZATION
[4.948s] -- looking for PCL_SURFACE
[4.949s] -- looking for PCL_REGISTRATION
[4.950s] -- looking for PCL_KEYPOINTS
[4.951s] -- looking for PCL_TRACKING
[4.952s] -- looking for PCL_RECOGNITION
[4.953s] -- looking for PCL_STEREO
[4.954s] -- looking for PCL_APPS
[4.956s] -- looking for PCL_IN_HAND_SCANNER
[4.957s] -- looking for PCL_MODELER
[4.957s] -- looking for PCL_POINT_CLOUD_EDITOR
[4.958s] -- looking for PCL_OUTOFCORE
[4.959s] -- looking for PCL_PEOPLE
[4.964s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[5.054s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[5.105s] -- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[5.158s] -- Found std_srvs: 4.8.0 (/opt/ros/humble/share/std_srvs/cmake)
[5.195s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[5.267s] -- Found octomap_msgs: 2.0.1 (/opt/ros/humble/share/octomap_msgs/cmake)
[5.310s] -- Found tf2: 0.25.13 (/opt/ros/humble/share/tf2/cmake)
[5.321s] -- Found tf2_ros: 0.25.13 (/opt/ros/humble/share/tf2_ros/cmake)
[5.498s] -- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[5.510s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[5.516s] -- Found Eigen3: TRUE (found version "3.4.0") 
[5.516s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[5.758s] -- Configuring done (5.7s)
[5.903s] -- Generating done (0.1s)
[5.943s] -- Build files have been written to: /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[5.997s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake /home/<USER>/fastlio2/octomap_ros2 -DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2
[6.005s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[6.092s] [ 33%] Built target octomap_server
[6.145s] [100%] Built target octomap_server2
[6.156s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --build /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2 -- -j6 -l6
[6.168s] Invoking command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
[6.175s] -- Install configuration: ""
[6.175s] -- Execute custom install script
[6.177s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server
[6.178s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so
[6.180s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py
[6.180s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2
[6.181s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2
[6.182s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh
[6.184s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv
[6.184s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh
[6.184s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv
[6.184s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash
[6.185s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh
[6.185s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh
[6.185s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv
[6.185s] -- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv
[6.194s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2
[6.195s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2
[6.195s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake
[6.196s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake
[6.196s] -- Up-to-date symlink: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml
[6.203s] Invoked command in '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/local/bin/cmake --install /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2
