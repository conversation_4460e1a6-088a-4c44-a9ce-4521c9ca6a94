[0.000000] (-) TimerEvent: {}
[0.001153] (octomap_server2) JobQueued: {'identifier': 'octomap_server2', 'dependencies': OrderedDict()}
[0.001480] (octomap_server2) JobStarted: {'identifier': 'octomap_server2'}
[0.018537] (octomap_server2) JobProgress: {'identifier': 'octomap_server2', 'progress': 'cmake'}
[0.019554] (octomap_server2) Command: {'cmd': ['/usr/local/bin/cmake', '/home/<USER>/fastlio2/octomap_ros2', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/fastlio2/octomap_ros2/install/octomap_server2'], 'cwd': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'nvidia'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('JETSON_L4T', '36.3.0'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin NX Engineering Reference Developer Kit'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1734'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'nvidia'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (8GB ram)'), ('JETSON_SERIAL_NUMBER', '1421224256350'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'nvidia'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/nvidia-desktop:@/tmp/.ICE-unix/1734,unix/nvidia-desktop:/tmp/.ICE-unix/1734'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/414342bb_5c5b_40d3_b637_5d3c77877ad3'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('GNOME_TERMINAL_SERVICE', ':1.126'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.0'), ('CMAKE_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0003')]), 'shell': False}
[0.099337] (-) TimerEvent: {}
[0.189979] (octomap_server2) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.199462] (-) TimerEvent: {}
[0.300093] (-) TimerEvent: {}
[0.331949] (octomap_server2) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.381822] (octomap_server2) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.400279] (-) TimerEvent: {}
[0.500976] (-) TimerEvent: {}
[0.525216] (octomap_server2) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.558515] (octomap_server2) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.560165] (octomap_server2) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.562213] (octomap_server2) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.588543] (octomap_server2) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.601109] (-) TimerEvent: {}
[0.701781] (-) TimerEvent: {}
[0.742397] (octomap_server2) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.773315] (octomap_server2) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.774262] (octomap_server2) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.775286] (octomap_server2) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.785189] (octomap_server2) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.802016] (-) TimerEvent: {}
[0.902878] (-) TimerEvent: {}
[1.003584] (-) TimerEvent: {}
[1.101718] (octomap_server2) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[1.103662] (-) TimerEvent: {}
[1.204201] (-) TimerEvent: {}
[1.250413] (octomap_server2) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[1.304417] (-) TimerEvent: {}
[1.348228] (octomap_server2) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[1.404595] (-) TimerEvent: {}
[1.466892] (octomap_server2) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.479830] (octomap_server2) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.502760] (octomap_server2) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.504641] (-) TimerEvent: {}
[1.540861] (octomap_server2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.594278] (octomap_server2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.604814] (-) TimerEvent: {}
[1.705466] (-) TimerEvent: {}
[1.723852] (octomap_server2) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.730980] (octomap_server2) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.805658] (-) TimerEvent: {}
[1.906475] (-) TimerEvent: {}
[1.913047] (octomap_server2) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[1.980630] (octomap_server2) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[2.006582] (-) TimerEvent: {}
[2.072777] (octomap_server2) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[2.094779] (octomap_server2) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[2.106715] (-) TimerEvent: {}
[2.207337] (-) TimerEvent: {}
[2.210384] (octomap_server2) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[2.213738] (octomap_server2) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[2.307516] (-) TimerEvent: {}
[2.408263] (-) TimerEvent: {}
[2.414502] (octomap_server2) StdoutLine: {'line': b'-- Found rclcpp_components: 16.0.12 (/opt/ros/humble/share/rclcpp_components/cmake)\n'}
[2.503509] (octomap_server2) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:146 (find_package):\n'}
[2.504034] (octomap_server2) StderrLine: {'line': b'  Policy CMP0144 is not set: find_package uses upper-case <PACKAGENAME>_ROOT\n'}
[2.504196] (octomap_server2) StderrLine: {'line': b'  variables.  Run "cmake --help-policy CMP0144" for policy details.  Use the\n'}
[2.504328] (octomap_server2) StderrLine: {'line': b'  cmake_policy command to set the policy and suppress this warning.\n'}
[2.504451] (octomap_server2) StderrLine: {'line': b'\n'}
[2.504570] (octomap_server2) StderrLine: {'line': b'  CMake variable EIGEN_ROOT is set to:\n'}
[2.504689] (octomap_server2) StderrLine: {'line': b'\n'}
[2.504803] (octomap_server2) StderrLine: {'line': b'    /usr/include/eigen3\n'}
[2.504914] (octomap_server2) StderrLine: {'line': b'\n'}
[2.505025] (octomap_server2) StderrLine: {'line': b'  For compatibility, find_package is ignoring the variable, but code in a\n'}
[2.505137] (octomap_server2) StderrLine: {'line': b'  .cmake module might still use it.\n'}
[2.505246] (octomap_server2) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.505354] (octomap_server2) StderrLine: {'line': b'  /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:299 (find_eigen)\n'}
[2.505461] (octomap_server2) StderrLine: {'line': b'  /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:543 (find_external_library)\n'}
[2.505568] (octomap_server2) StderrLine: {'line': b'  CMakeLists.txt:21 (find_package)\n'}
[2.505675] (octomap_server2) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.505784] (octomap_server2) StderrLine: {'line': b'\x1b[0m\n'}
[2.508351] (-) TimerEvent: {}
[2.512302] (octomap_server2) StdoutLine: {'line': b"-- Checking for module 'eigen3'\n"}
[2.538353] (octomap_server2) StdoutLine: {'line': b'--   Found eigen3, version 3.4.0\n'}
[2.593199] (octomap_server2) StdoutLine: {'line': b'-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") \n'}
[2.593843] (octomap_server2) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[2.608550] (-) TimerEvent: {}
[2.652961] (octomap_server2) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found suitable version "1.74.0", minimum required is "1.65.0") found components: system filesystem date_time iostreams serialization \n'}
[2.658527] (octomap_server2) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /usr/lib/aarch64-linux-gnu/cmake/pcl/Modules/FindFLANN.cmake:44 (find_package):\n'}
[2.659073] (octomap_server2) StderrLine: {'line': b'  Policy CMP0144 is not set: find_package uses upper-case <PACKAGENAME>_ROOT\n'}
[2.659273] (octomap_server2) StderrLine: {'line': b'  variables.  Run "cmake --help-policy CMP0144" for policy details.  Use the\n'}
[2.659463] (octomap_server2) StderrLine: {'line': b'  cmake_policy command to set the policy and suppress this warning.\n'}
[2.659610] (octomap_server2) StderrLine: {'line': b'\n'}
[2.660104] (octomap_server2) StderrLine: {'line': b'  CMake variable FLANN_ROOT is set to:\n'}
[2.660263] (octomap_server2) StderrLine: {'line': b'\n'}
[2.660427] (octomap_server2) StderrLine: {'line': b'    /usr\n'}
[2.660551] (octomap_server2) StderrLine: {'line': b'\n'}
[2.660667] (octomap_server2) StderrLine: {'line': b'  For compatibility, find_package is ignoring the variable, but code in a\n'}
[2.660784] (octomap_server2) StderrLine: {'line': b'  .cmake module might still use it.\n'}
[2.660944] (octomap_server2) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.661131] (octomap_server2) StderrLine: {'line': b'  /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:256 (find_package)\n'}
[2.661255] (octomap_server2) StderrLine: {'line': b'  /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:301 (find_flann)\n'}
[2.661409] (octomap_server2) StderrLine: {'line': b'  /usr/lib/aarch64-linux-gnu/cmake/pcl/PCLConfig.cmake:543 (find_external_library)\n'}
[2.661525] (octomap_server2) StderrLine: {'line': b'  CMakeLists.txt:21 (find_package)\n'}
[2.661640] (octomap_server2) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.661751] (octomap_server2) StderrLine: {'line': b'\x1b[0m\n'}
[2.687079] (octomap_server2) StdoutLine: {'line': b"-- Checking for module 'flann'\n"}
[2.708731] (-) TimerEvent: {}
[2.710010] (octomap_server2) StdoutLine: {'line': b'--   Found flann, version 1.9.1\n'}
[2.794990] (octomap_server2) StdoutLine: {'line': b'-- Found FLANN: /usr/lib/aarch64-linux-gnu/libflann_cpp.so  \n'}
[2.795578] (octomap_server2) StdoutLine: {'line': b'-- FLANN found (include: /usr/include, lib: /usr/lib/aarch64-linux-gnu/libflann_cpp.so)\n'}
[2.808901] (-) TimerEvent: {}
[2.902964] (octomap_server2) StdoutLine: {'line': b'-- Found GLEW: /usr/lib/aarch64-linux-gnu/libGLEW.so  \n'}
[2.909058] (-) TimerEvent: {}
[2.917526] (octomap_server2) StdoutLine: {'line': b'-- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so  found components: OpenGL GLX \n'}
[3.009294] (-) TimerEvent: {}
[3.109975] (-) TimerEvent: {}
[3.197989] (octomap_server2) StdoutLine: {'line': b'-- Found MPI_C: /usr/lib/aarch64-linux-gnu/libmpi.so (found version "3.1") \n'}
[3.199706] (octomap_server2) StdoutLine: {'line': b'-- Found MPI: TRUE (found version "3.1") found components: C \n'}
[3.210133] (-) TimerEvent: {}
[3.210874] (octomap_server2) StdoutLine: {'line': b'-- Found JsonCpp: /usr/lib/aarch64-linux-gnu/libjsoncpp.so (found suitable version "1.9.5", minimum required is "0.7.0") \n'}
[3.262733] (octomap_server2) StdoutLine: {'line': b'-- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")  \n'}
[3.310582] (-) TimerEvent: {}
[3.332893] (octomap_server2) StdoutLine: {'line': b'-- Found PNG: /usr/lib/aarch64-linux-gnu/libpng.so (found version "1.6.37") \n'}
[3.382943] (octomap_server2) StdoutLine: {'line': b'-- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") \n'}
[3.410683] (-) TimerEvent: {}
[3.511398] (-) TimerEvent: {}
[3.612159] (-) TimerEvent: {}
[3.636880] (octomap_server2) StdoutLine: {'line': b'-- Found X11: /usr/include   \n'}
[3.638866] (octomap_server2) StdoutLine: {'line': b'-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so\n'}
[3.712353] (-) TimerEvent: {}
[3.765232] (octomap_server2) StdoutLine: {'line': b'-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found\n'}
[3.765795] (octomap_server2) StdoutLine: {'line': b'-- Looking for gethostbyname\n'}
[3.812518] (-) TimerEvent: {}
[3.904262] (octomap_server2) StdoutLine: {'line': b'-- Looking for gethostbyname - found\n'}
[3.905006] (octomap_server2) StdoutLine: {'line': b'-- Looking for connect\n'}
[3.912673] (-) TimerEvent: {}
[4.013390] (-) TimerEvent: {}
[4.025756] (octomap_server2) StdoutLine: {'line': b'-- Looking for connect - found\n'}
[4.026301] (octomap_server2) StdoutLine: {'line': b'-- Looking for remove\n'}
[4.113546] (-) TimerEvent: {}
[4.157559] (octomap_server2) StdoutLine: {'line': b'-- Looking for remove - found\n'}
[4.158503] (octomap_server2) StdoutLine: {'line': b'-- Looking for shmat\n'}
[4.213712] (-) TimerEvent: {}
[4.284983] (octomap_server2) StdoutLine: {'line': b'-- Looking for shmat - found\n'}
[4.285619] (octomap_server2) StdoutLine: {'line': b'-- Looking for IceConnectionNumber in ICE\n'}
[4.313911] (-) TimerEvent: {}
[4.414611] (-) TimerEvent: {}
[4.415817] (octomap_server2) StdoutLine: {'line': b'-- Looking for IceConnectionNumber in ICE - found\n'}
[4.514959] (-) TimerEvent: {}
[4.615654] (-) TimerEvent: {}
[4.653348] (octomap_server2) StdoutLine: {'line': b'-- Found EXPAT: /usr/lib/aarch64-linux-gnu/libexpat.so (found version "2.4.7") \n'}
[4.660810] (octomap_server2) StdoutLine: {'line': b'-- Found double-conversion: /usr/lib/aarch64-linux-gnu/libdouble-conversion.so  \n'}
[4.670197] (octomap_server2) StdoutLine: {'line': b'-- Found LZ4: /usr/lib/aarch64-linux-gnu/liblz4.so (found version "1.9.3") \n'}
[4.677398] (octomap_server2) StdoutLine: {'line': b'-- Found LZMA: /usr/lib/aarch64-linux-gnu/liblzma.so (found version "5.2.5") \n'}
[4.715825] (-) TimerEvent: {}
[4.743516] (octomap_server2) StdoutLine: {'line': b'-- Found JPEG: /usr/lib/aarch64-linux-gnu/libjpeg.so (found version "80") \n'}
[4.761704] (octomap_server2) StdoutLine: {'line': b'-- Found TIFF: /usr/lib/aarch64-linux-gnu/libtiff.so (found version "4.3.0")  \n'}
[4.779814] (octomap_server2) StdoutLine: {'line': b'-- Found Freetype: /usr/lib/aarch64-linux-gnu/libfreetype.so (found version "2.11.1") \n'}
[4.793879] (octomap_server2) StdoutLine: {'line': b'-- Found utf8cpp: /usr/include/utf8cpp  \n'}
[4.815954] (-) TimerEvent: {}
[4.916581] (-) TimerEvent: {}
[4.961281] (octomap_server2) StdoutLine: {'line': b"-- Checking for module 'libusb-1.0'\n"}
[4.985158] (octomap_server2) StdoutLine: {'line': b'--   Found libusb-1.0, version 1.0.25\n'}
[5.016753] (-) TimerEvent: {}
[5.061942] (octomap_server2) StdoutLine: {'line': b'-- Found libusb: /usr/lib/aarch64-linux-gnu/libusb-1.0.so  \n'}
[5.067274] (octomap_server2) StdoutLine: {'line': b'-- Found OpenNI: /usr/lib/libOpenNI.so;libusb::libusb (found version "1.5.4.0") \n'}
[5.067819] (octomap_server2) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[5.116952] (-) TimerEvent: {}
[5.191188] (octomap_server2) StdoutLine: {'line': b'-- Found OpenNI2: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb (found version "2.2.0.33") \n'}
[5.191761] (octomap_server2) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[5.192027] (octomap_server2) StderrLine: {'line': b'\x1b[0m** WARNING ** io features related to pcap will be disabled\x1b[0m\n'}
[5.217130] (-) TimerEvent: {}
[5.317880] (-) TimerEvent: {}
[5.418547] (-) TimerEvent: {}
[5.519217] (-) TimerEvent: {}
[5.619872] (-) TimerEvent: {}
[5.720531] (-) TimerEvent: {}
[5.747309] (octomap_server2) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[5.761704] (octomap_server2) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[5.774609] (octomap_server2) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[5.820690] (-) TimerEvent: {}
[5.921403] (-) TimerEvent: {}
[6.022119] (-) TimerEvent: {}
[6.122787] (-) TimerEvent: {}
[6.223579] (-) TimerEvent: {}
[6.282884] (octomap_server2) StdoutLine: {'line': b'-- Found Qhull version 8.0.2\n'}
[6.323751] (-) TimerEvent: {}
[6.424448] (-) TimerEvent: {}
[6.525170] (-) TimerEvent: {}
[6.625986] (-) TimerEvent: {}
[6.726690] (-) TimerEvent: {}
[6.806719] (octomap_server2) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[6.826847] (-) TimerEvent: {}
[6.927530] (-) TimerEvent: {}
[7.028271] (-) TimerEvent: {}
[7.128991] (-) TimerEvent: {}
[7.229714] (-) TimerEvent: {}
[7.330417] (-) TimerEvent: {}
[7.343055] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_COMMON\n'}
[7.346096] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_COMMON: /usr/lib/aarch64-linux-gnu/libpcl_common.so  \n'}
[7.346599] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_KDTREE\n'}
[7.348557] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_KDTREE: /usr/lib/aarch64-linux-gnu/libpcl_kdtree.so  \n'}
[7.349004] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_OCTREE\n'}
[7.350899] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_OCTREE: /usr/lib/aarch64-linux-gnu/libpcl_octree.so  \n'}
[7.351325] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_SEARCH\n'}
[7.354178] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_SEARCH: /usr/lib/aarch64-linux-gnu/libpcl_search.so  \n'}
[7.354704] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_SAMPLE_CONSENSUS\n'}
[7.357578] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/aarch64-linux-gnu/libpcl_sample_consensus.so  \n'}
[7.358026] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_FILTERS\n'}
[7.360553] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_FILTERS: /usr/lib/aarch64-linux-gnu/libpcl_filters.so  \n'}
[7.361393] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_2D\n'}
[7.362123] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_2D: /usr/include/pcl-1.12  \n'}
[7.362592] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_GEOMETRY\n'}
[7.363266] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  \n'}
[7.363875] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_IO\n'}
[7.366314] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_IO: /usr/lib/aarch64-linux-gnu/libpcl_io.so  \n'}
[7.366943] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_FEATURES\n'}
[7.368963] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_FEATURES: /usr/lib/aarch64-linux-gnu/libpcl_features.so  \n'}
[7.369613] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_ML\n'}
[7.371719] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_ML: /usr/lib/aarch64-linux-gnu/libpcl_ml.so  \n'}
[7.372133] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_SEGMENTATION\n'}
[7.374357] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_SEGMENTATION: /usr/lib/aarch64-linux-gnu/libpcl_segmentation.so  \n'}
[7.375070] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_VISUALIZATION\n'}
[7.377189] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_VISUALIZATION: /usr/lib/aarch64-linux-gnu/libpcl_visualization.so  \n'}
[7.377904] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_SURFACE\n'}
[7.379903] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_SURFACE: /usr/lib/aarch64-linux-gnu/libpcl_surface.so  \n'}
[7.380478] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_REGISTRATION\n'}
[7.382660] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_REGISTRATION: /usr/lib/aarch64-linux-gnu/libpcl_registration.so  \n'}
[7.383386] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_KEYPOINTS\n'}
[7.385833] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_KEYPOINTS: /usr/lib/aarch64-linux-gnu/libpcl_keypoints.so  \n'}
[7.386536] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_TRACKING\n'}
[7.388696] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_TRACKING: /usr/lib/aarch64-linux-gnu/libpcl_tracking.so  \n'}
[7.389323] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_RECOGNITION\n'}
[7.391554] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_RECOGNITION: /usr/lib/aarch64-linux-gnu/libpcl_recognition.so  \n'}
[7.392388] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_STEREO\n'}
[7.394738] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_STEREO: /usr/lib/aarch64-linux-gnu/libpcl_stereo.so  \n'}
[7.395337] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_APPS\n'}
[7.397531] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_APPS: /usr/lib/aarch64-linux-gnu/libpcl_apps.so  \n'}
[7.399679] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_IN_HAND_SCANNER\n'}
[7.400514] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  \n'}
[7.400917] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_MODELER\n'}
[7.401622] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_MODELER: /usr/include/pcl-1.12  \n'}
[7.402040] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_POINT_CLOUD_EDITOR\n'}
[7.403100] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  \n'}
[7.403616] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_OUTOFCORE\n'}
[7.405795] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_OUTOFCORE: /usr/lib/aarch64-linux-gnu/libpcl_outofcore.so  \n'}
[7.406353] (octomap_server2) StdoutLine: {'line': b'-- looking for PCL_PEOPLE\n'}
[7.408739] (octomap_server2) StdoutLine: {'line': b'-- Found PCL_PEOPLE: /usr/lib/aarch64-linux-gnu/libpcl_people.so  \n'}
[7.411525] (octomap_server2) StdoutLine: {'line': b'-- Found PCL: pcl_common;pcl_kdtree;pcl_octree;pcl_search;pcl_sample_consensus;pcl_filters;pcl_io;pcl_features;pcl_ml;pcl_segmentation;pcl_visualization;pcl_surface;pcl_registration;pcl_keypoints;pcl_tracking;pcl_recognition;pcl_stereo;pcl_apps;pcl_outofcore;pcl_people;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;/usr/lib/libOpenNI.so;libusb::libusb;/usr/lib/aarch64-linux-gnu/libOpenNI2.so;libusb::libusb;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::GUISupportQt;FLANN::FLANN;QHULL::QHULL (Required is at least version "1.10") \n'}
[7.417344] (octomap_server2) StdoutLine: {'line': b'-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[7.430499] (-) TimerEvent: {}
[7.525082] (octomap_server2) StdoutLine: {'line': b'-- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[7.530631] (-) TimerEvent: {}
[7.577426] (octomap_server2) StdoutLine: {'line': b'-- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[7.630808] (-) TimerEvent: {}
[7.643095] (octomap_server2) StdoutLine: {'line': b'-- Found std_srvs: 4.8.0 (/opt/ros/humble/share/std_srvs/cmake)\n'}
[7.731064] (-) TimerEvent: {}
[7.831895] (-) TimerEvent: {}
[7.932693] (-) TimerEvent: {}
[8.033520] (-) TimerEvent: {}
[8.125346] (octomap_server2) StdoutLine: {'line': b'-- Found OpenMP_C: -fopenmp (found version "4.5") \n'}
[8.133620] (-) TimerEvent: {}
[8.234334] (-) TimerEvent: {}
[8.299926] (octomap_server2) StdoutLine: {'line': b'-- Found OpenMP_CXX: -fopenmp (found version "4.5") \n'}
[8.300812] (octomap_server2) StdoutLine: {'line': b'-- Found OpenMP: TRUE (found version "4.5")  \n'}
[8.307024] (octomap_server2) StdoutLine: {'line': b'-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)\n'}
[8.334472] (-) TimerEvent: {}
[8.421791] (octomap_server2) StdoutLine: {'line': b'-- Found octomap_msgs: 2.0.1 (/opt/ros/humble/share/octomap_msgs/cmake)\n'}
[8.434681] (-) TimerEvent: {}
[8.469053] (octomap_server2) StdoutLine: {'line': b'-- Found tf2: 0.25.13 (/opt/ros/humble/share/tf2/cmake)\n'}
[8.481549] (octomap_server2) StdoutLine: {'line': b'-- Found tf2_ros: 0.25.13 (/opt/ros/humble/share/tf2_ros/cmake)\n'}
[8.534872] (-) TimerEvent: {}
[8.635689] (-) TimerEvent: {}
[8.660145] (octomap_server2) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[8.694098] (octomap_server2) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[8.698645] (octomap_server2) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[8.699204] (octomap_server2) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[8.735884] (-) TimerEvent: {}
[8.836623] (-) TimerEvent: {}
[8.890993] (octomap_server2) StdoutLine: {'line': b'-- Configuring done (8.9s)\n'}
[8.936783] (-) TimerEvent: {}
[9.017117] (octomap_server2) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[9.035229] (octomap_server2) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2\n'}
[9.036905] (-) TimerEvent: {}
[9.095542] (octomap_server2) CommandEnded: {'returncode': 0}
[9.097894] (octomap_server2) JobProgress: {'identifier': 'octomap_server2', 'progress': 'build'}
[9.101038] (octomap_server2) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', '--', '-j6', '-l6'], 'cwd': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'nvidia'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('JETSON_L4T', '36.3.0'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin NX Engineering Reference Developer Kit'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1734'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'nvidia'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (8GB ram)'), ('JETSON_SERIAL_NUMBER', '1421224256350'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'nvidia'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/nvidia-desktop:@/tmp/.ICE-unix/1734,unix/nvidia-desktop:/tmp/.ICE-unix/1734'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/414342bb_5c5b_40d3_b637_5d3c77877ad3'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('GNOME_TERMINAL_SERVICE', ':1.126'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.0'), ('CMAKE_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0003')]), 'shell': False}
[9.137131] (-) TimerEvent: {}
[9.155823] (octomap_server2) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding CXX object CMakeFiles/octomap_server.dir/rclcpp_components/node_main_octomap_server.cpp.o\x1b[0m\n'}
[9.157210] (octomap_server2) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/conversions.cpp.o\x1b[0m\n'}
[9.157460] (octomap_server2) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.o\x1b[0m\n'}
[9.159343] (octomap_server2) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/octomap_server2.dir/src/transforms.cpp.o\x1b[0m\n'}
[9.238849] (-) TimerEvent: {}
[9.339721] (-) TimerEvent: {}
[9.440408] (-) TimerEvent: {}
[9.541086] (-) TimerEvent: {}
[9.641762] (-) TimerEvent: {}
[9.648656] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/conversions.h:50\x1b[m\x1b[K,\n'}
[9.649164] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/conversions.cpp:40\x1b[m\x1b[K:\n'}
[9.649409] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.649593] (octomap_server2) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[9.649729] (octomap_server2) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[9.741953] (-) TimerEvent: {}
[9.842657] (-) TimerEvent: {}
[9.943373] (-) TimerEvent: {}
[10.044047] (-) TimerEvent: {}
[10.144732] (-) TimerEvent: {}
[10.245420] (-) TimerEvent: {}
[10.346258] (-) TimerEvent: {}
[10.447103] (-) TimerEvent: {}
[10.547783] (-) TimerEvent: {}
[10.648595] (-) TimerEvent: {}
[10.749287] (-) TimerEvent: {}
[10.849985] (-) TimerEvent: {}
[10.950699] (-) TimerEvent: {}
[11.051401] (-) TimerEvent: {}
[11.152089] (-) TimerEvent: {}
[11.252801] (-) TimerEvent: {}
[11.353477] (-) TimerEvent: {}
[11.454181] (-) TimerEvent: {}
[11.554847] (-) TimerEvent: {}
[11.655536] (-) TimerEvent: {}
[11.756287] (-) TimerEvent: {}
[11.856973] (-) TimerEvent: {}
[11.957659] (-) TimerEvent: {}
[12.058353] (-) TimerEvent: {}
[12.159057] (-) TimerEvent: {}
[12.259731] (-) TimerEvent: {}
[12.360432] (-) TimerEvent: {}
[12.461133] (-) TimerEvent: {}
[12.561808] (-) TimerEvent: {}
[12.662542] (-) TimerEvent: {}
[12.763239] (-) TimerEvent: {}
[12.863930] (-) TimerEvent: {}
[12.964625] (-) TimerEvent: {}
[13.065330] (-) TimerEvent: {}
[13.166029] (-) TimerEvent: {}
[13.266757] (-) TimerEvent: {}
[13.367457] (-) TimerEvent: {}
[13.468154] (-) TimerEvent: {}
[13.560971] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/conversions.h:50\x1b[m\x1b[K,\n'}
[13.561964] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:51\x1b[m\x1b[K,\n'}
[13.562220] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[13.562369] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[13.562511] (octomap_server2) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[13.562638] (octomap_server2) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[13.568294] (-) TimerEvent: {}
[13.668923] (-) TimerEvent: {}
[13.769632] (-) TimerEvent: {}
[13.870341] (-) TimerEvent: {}
[13.971000] (-) TimerEvent: {}
[14.071687] (-) TimerEvent: {}
[14.172353] (-) TimerEvent: {}
[14.273028] (-) TimerEvent: {}
[14.373724] (-) TimerEvent: {}
[14.474467] (-) TimerEvent: {}
[14.575141] (-) TimerEvent: {}
[14.675815] (-) TimerEvent: {}
[14.776495] (-) TimerEvent: {}
[14.877170] (-) TimerEvent: {}
[14.977849] (-) TimerEvent: {}
[15.078576] (-) TimerEvent: {}
[15.179252] (-) TimerEvent: {}
[15.279951] (-) TimerEvent: {}
[15.380671] (-) TimerEvent: {}
[15.481361] (-) TimerEvent: {}
[15.582057] (-) TimerEvent: {}
[15.682762] (-) TimerEvent: {}
[15.783457] (-) TimerEvent: {}
[15.884360] (-) TimerEvent: {}
[15.985051] (-) TimerEvent: {}
[16.085713] (-) TimerEvent: {}
[16.164228] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/conversions.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid octomap::pointsOctomapToPointCloud2(const point3d_list&, sensor_msgs::msg::PointCloud2&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[16.164813] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/conversions.cpp:53:57:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kpoints\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[16.165040] (octomap_server2) StderrLine: {'line': b'   53 |     void pointsOctomapToPointCloud2(\x1b[01;35m\x1b[Kconst point3d_list& points\x1b[m\x1b[K,\n'}
[16.165241] (octomap_server2) StderrLine: {'line': b'      |                                     \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[16.185925] (-) TimerEvent: {}
[16.286613] (-) TimerEvent: {}
[16.387377] (-) TimerEvent: {}
[16.488100] (-) TimerEvent: {}
[16.588823] (-) TimerEvent: {}
[16.689627] (-) TimerEvent: {}
[16.790394] (-) TimerEvent: {}
[16.891100] (-) TimerEvent: {}
[16.991836] (-) TimerEvent: {}
[17.043926] (octomap_server2) StdoutLine: {'line': b'[ 83%] \x1b[32m\x1b[1mLinking CXX executable octomap_server\x1b[0m\n'}
[17.092029] (-) TimerEvent: {}
[17.192716] (-) TimerEvent: {}
[17.293410] (-) TimerEvent: {}
[17.297192] (octomap_server2) StdoutLine: {'line': b'[ 83%] Built target octomap_server\n'}
[17.393589] (-) TimerEvent: {}
[17.494245] (-) TimerEvent: {}
[17.594910] (-) TimerEvent: {}
[17.695599] (-) TimerEvent: {}
[17.796258] (-) TimerEvent: {}
[17.896965] (-) TimerEvent: {}
[17.997667] (-) TimerEvent: {}
[18.098402] (-) TimerEvent: {}
[18.199083] (-) TimerEvent: {}
[18.299747] (-) TimerEvent: {}
[18.400415] (-) TimerEvent: {}
[18.501189] (-) TimerEvent: {}
[18.602165] (-) TimerEvent: {}
[18.702901] (-) TimerEvent: {}
[18.803630] (-) TimerEvent: {}
[18.904367] (-) TimerEvent: {}
[19.005025] (-) TimerEvent: {}
[19.105679] (-) TimerEvent: {}
[19.206428] (-) TimerEvent: {}
[19.307155] (-) TimerEvent: {}
[19.407938] (-) TimerEvent: {}
[19.508535] (-) TimerEvent: {}
[19.609127] (-) TimerEvent: {}
[19.709737] (-) TimerEvent: {}
[19.810368] (-) TimerEvent: {}
[19.910975] (-) TimerEvent: {}
[20.011574] (-) TimerEvent: {}
[20.112178] (-) TimerEvent: {}
[20.212793] (-) TimerEvent: {}
[20.313408] (-) TimerEvent: {}
[20.414086] (-) TimerEvent: {}
[20.514769] (-) TimerEvent: {}
[20.615473] (-) TimerEvent: {}
[20.716155] (-) TimerEvent: {}
[20.816844] (-) TimerEvent: {}
[20.918376] (-) TimerEvent: {}
[21.019049] (-) TimerEvent: {}
[21.119726] (-) TimerEvent: {}
[21.220398] (-) TimerEvent: {}
[21.321126] (-) TimerEvent: {}
[21.421889] (-) TimerEvent: {}
[21.522589] (-) TimerEvent: {}
[21.623281] (-) TimerEvent: {}
[21.724047] (-) TimerEvent: {}
[21.824897] (-) TimerEvent: {}
[21.925586] (-) TimerEvent: {}
[22.026340] (-) TimerEvent: {}
[22.127017] (-) TimerEvent: {}
[22.227678] (-) TimerEvent: {}
[22.328347] (-) TimerEvent: {}
[22.429023] (-) TimerEvent: {}
[22.529731] (-) TimerEvent: {}
[22.630435] (-) TimerEvent: {}
[22.731136] (-) TimerEvent: {}
[22.831830] (-) TimerEvent: {}
[22.934283] (-) TimerEvent: {}
[23.035132] (-) TimerEvent: {}
[23.135966] (-) TimerEvent: {}
[23.236786] (-) TimerEvent: {}
[23.337571] (-) TimerEvent: {}
[23.438480] (-) TimerEvent: {}
[23.539289] (-) TimerEvent: {}
[23.640111] (-) TimerEvent: {}
[23.740928] (-) TimerEvent: {}
[23.841741] (-) TimerEvent: {}
[23.942569] (-) TimerEvent: {}
[24.043397] (-) TimerEvent: {}
[24.144273] (-) TimerEvent: {}
[24.245127] (-) TimerEvent: {}
[24.345954] (-) TimerEvent: {}
[24.446835] (-) TimerEvent: {}
[24.547714] (-) TimerEvent: {}
[24.648570] (-) TimerEvent: {}
[24.749401] (-) TimerEvent: {}
[24.850258] (-) TimerEvent: {}
[24.951071] (-) TimerEvent: {}
[25.051890] (-) TimerEvent: {}
[25.152700] (-) TimerEvent: {}
[25.253545] (-) TimerEvent: {}
[25.354401] (-) TimerEvent: {}
[25.455261] (-) TimerEvent: {}
[25.467040] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[25.467735] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handleNode(const iterator&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.468394] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:210:58:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kit\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.468820] (octomap_server2) StderrLine: {'line': b'  210 |         virtual void handleNode(\x1b[01;35m\x1b[Kconst OcTreeT::iterator& it\x1b[m\x1b[K) {};\n'}
[25.469089] (octomap_server2) StderrLine: {'line': b'      |                                 \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~^~\x1b[m\x1b[K\n'}
[25.469333] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handleNodeInBBX(const iterator&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.469570] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:211:63:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kit\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.469817] (octomap_server2) StderrLine: {'line': b'  211 |         virtual void handleNodeInBBX(\x1b[01;35m\x1b[Kconst OcTreeT::iterator& it\x1b[m\x1b[K) {};\n'}
[25.470045] (octomap_server2) StderrLine: {'line': b'      |                                      \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~^~\x1b[m\x1b[K\n'}
[25.470299] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Koctomap_server::OctomapServer::OctomapServer(const rclcpp::NodeOptions&, std::string)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.470522] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:148:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Koctomap_server::OctomapServer::m_useColoredMap\x1b[m\x1b[K\xe2\x80\x99 will be initialized after [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.470747] (octomap_server2) StderrLine: {'line': b'  148 |         bool \x1b[01;35m\x1b[Km_useColoredMap\x1b[m\x1b[K;\n'}
[25.470959] (octomap_server2) StderrLine: {'line': b'      |              \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[25.471211] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:118:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  \xe2\x80\x98\x1b[01m\x1b[Kdouble octomap_server::OctomapServer::m_colorFactor\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.471437] (octomap_server2) StderrLine: {'line': b'  118 |         double \x1b[01;35m\x1b[Km_colorFactor\x1b[m\x1b[K;\n'}
[25.471648] (octomap_server2) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[25.471863] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:5:5:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  when initialized here [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.472604] (octomap_server2) StderrLine: {'line': b'    5 |     \x1b[01;35m\x1b[KOctomapServer\x1b[m\x1b[K::OctomapServer(\n'}
[25.472857] (octomap_server2) StderrLine: {'line': b'      |     \x1b[01;35m\x1b[K^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[25.555415] (-) TimerEvent: {}
[25.656230] (-) TimerEvent: {}
[25.718568] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[25.719749] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[25.720065] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[25.720324] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[25.720613] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[25.720847] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[25.721066] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[25.721284] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[25.721492] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[25.721703] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[25.721906] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[25.722554] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8\x1b[m\x1b[K,\n'}
[25.722874] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[25.723102] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::insertCloudCallback(const ConstSharedPtr&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.723376] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:50:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kformat \xe2\x80\x98\x1b[01m\x1b[K%s\x1b[m\x1b[K\xe2\x80\x99 expects argument of type \xe2\x80\x98\x1b[01m\x1b[Kchar*\x1b[m\x1b[K\xe2\x80\x99, but argument 5 has type \xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::__cxx11::basic_string<char>\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=\x07-Wformat=\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.723615] (octomap_server2) StderrLine: {'line': b'  341 |                 RCLCPP_ERROR(this->get_logger(), \x1b[01;35m\x1b[K"%s %"\x1b[m\x1b[K, msg, ex.what());\n'}
[25.723829] (octomap_server2) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[25.724039] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:52:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kformat string is defined here\n'}
[25.724249] (octomap_server2) StderrLine: {'line': b'  341 |                 RCLCPP_ERROR(this->get_logger(), "\x1b[01;36m\x1b[K%s\x1b[m\x1b[K %", msg, ex.what());\n'}
[25.724493] (octomap_server2) StderrLine: {'line': b'      |                                                   \x1b[01;36m\x1b[K~^\x1b[m\x1b[K\n'}
[25.724702] (octomap_server2) StderrLine: {'line': b'      |                                                    \x1b[01;36m\x1b[K|\x1b[m\x1b[K\n'}
[25.724904] (octomap_server2) StderrLine: {'line': b'      |                                                    \x1b[01;36m\x1b[Kchar*\x1b[m\x1b[K\n'}
[25.725106] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[25.725309] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[25.725512] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[25.725713] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[25.725912] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[25.726171] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[25.726404] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[25.726614] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[25.726821] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[25.727064] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[25.727270] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[25.727473] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8\x1b[m\x1b[K,\n'}
[25.727676] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[25.727877] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:50:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kspurious trailing \xe2\x80\x98\x1b[01m\x1b[K%\x1b[m\x1b[K\xe2\x80\x99 in format [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=\x07-Wformat=\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.728095] (octomap_server2) StderrLine: {'line': b'  341 |                 RCLCPP_ERROR(this->get_logger(), \x1b[01;35m\x1b[K"%s %"\x1b[m\x1b[K, msg, ex.what());\n'}
[25.728321] (octomap_server2) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[25.728531] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:54:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kformat string is defined here\n'}
[25.728741] (octomap_server2) StderrLine: {'line': b'  341 |                 RCLCPP_ERROR(this->get_logger(), "%s \x1b[01;36m\x1b[K%\x1b[m\x1b[K", msg, ex.what());\n'}
[25.728945] (octomap_server2) StderrLine: {'line': b'      |                                                      \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[25.729146] (octomap_server2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[25.729372] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[25.729574] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[25.729774] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[25.729974] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[25.730212] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[25.730419] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[25.730622] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[25.730825] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[25.732617] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[25.732976] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[25.733231] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/include/octomap_server2/octomap_server.hpp:8\x1b[m\x1b[K,\n'}
[25.733466] (octomap_server2) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:2\x1b[m\x1b[K:\n'}
[25.733690] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:341:50:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Ktoo many arguments for format [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat-extra-args\x07-Wformat-extra-args\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.733918] (octomap_server2) StderrLine: {'line': b'  341 |                 RCLCPP_ERROR(this->get_logger(), \x1b[01;35m\x1b[K"%s %"\x1b[m\x1b[K, msg, ex.what());\n'}
[25.734174] (octomap_server2) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[25.751561] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::publishAll(const rclcpp::Time&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.752083] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:595:28:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Ksize\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.752359] (octomap_server2) StderrLine: {'line': b'  595 |                     double \x1b[01;35m\x1b[Ksize\x1b[m\x1b[K = it.getSize();\n'}
[25.752593] (octomap_server2) StderrLine: {'line': b'      |                            \x1b[01;35m\x1b[K^~~~\x1b[m\x1b[K\n'}
[25.756318] (-) TimerEvent: {}
[25.767605] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual bool octomap_server::OctomapServer::octomapBinarySrv(std::shared_ptr<octomap_msgs::srv::GetOctomap_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::GetOctomap_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.768034] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:783:52:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kreq\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.768329] (octomap_server2) StderrLine: {'line': b'  783 |         \x1b[01;35m\x1b[Kconst std::shared_ptr<OctomapSrv::Request> req\x1b[m\x1b[K,\n'}
[25.768564] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[25.769204] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual bool octomap_server::OctomapServer::octomapFullSrv(std::shared_ptr<octomap_msgs::srv::GetOctomap_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::GetOctomap_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.769489] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:802:52:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kreq\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.769730] (octomap_server2) StderrLine: {'line': b'  802 |         \x1b[01;35m\x1b[Kconst std::shared_ptr<OctomapSrv::Request> req\x1b[m\x1b[K,\n'}
[25.769949] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[25.779599] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool octomap_server::OctomapServer::clearBBXSrv(std::shared_ptr<octomap_msgs::srv::BoundingBoxQuery_Request_<std::allocator<void> > >, std::shared_ptr<octomap_msgs::srv::BoundingBoxQuery_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.779926] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:817:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kresp\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.780169] (octomap_server2) StderrLine: {'line': b'  817 |         \x1b[01;35m\x1b[Kstd::shared_ptr<BBXSrv::Response> resp\x1b[m\x1b[K) {\n'}
[25.780389] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~\x1b[m\x1b[K\n'}
[25.790927] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool octomap_server::OctomapServer::resetSrv(std::shared_ptr<std_srvs::srv::Empty_Request_<std::allocator<void> > >, std::shared_ptr<std_srvs::srv::Empty_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.791350] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:855:28:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<visualization_msgs::msg::Marker_<std::allocator<void> >, std::allocator<visualization_msgs::msg::Marker_<std::allocator<void> > > >::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.791721] (octomap_server2) StderrLine: {'line': b'  855 |         for (auto i = 0; \x1b[01;35m\x1b[Ki < occupiedNodesVis.markers.size()\x1b[m\x1b[K; ++i){\n'}
[25.792002] (octomap_server2) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[25.792377] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:871:28:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<visualization_msgs::msg::Marker_<std::allocator<void> >, std::allocator<visualization_msgs::msg::Marker_<std::allocator<void> > > >::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.792639] (octomap_server2) StderrLine: {'line': b'  871 |         for (auto i = 0; \x1b[01;35m\x1b[Ki < freeNodesVis.markers.size()\x1b[m\x1b[K; ++i) {\n'}
[25.793046] (octomap_server2) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[25.793282] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:835:62:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kreq\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.793513] (octomap_server2) StderrLine: {'line': b'  835 |         \x1b[01;35m\x1b[Kconst std::shared_ptr<std_srvs::srv::Empty::Request> req\x1b[m\x1b[K,\n'}
[25.793724] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[25.793931] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:836:57:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kresp\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.794244] (octomap_server2) StderrLine: {'line': b'  836 |         \x1b[01;35m\x1b[Kstd::shared_ptr<std_srvs::srv::Empty::Response> resp\x1b[m\x1b[K) {\n'}
[25.794466] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~\x1b[m\x1b[K\n'}
[25.855124] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handlePreNodeTraversal(const rclcpp::Time&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.855812] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:1163:59:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kunsigned int\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.856153] (octomap_server2) StderrLine: {'line': b' 1163 |                 for (unsigned int j = mapUpdateBBXMinY; \x1b[01;35m\x1b[Kj <= mapUpdateBBXMaxY\x1b[m\x1b[K; ++j) {\n'}
[25.856421] (-) TimerEvent: {}
[25.856801] (octomap_server2) StderrLine: {'line': b'      |                                                         \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[25.858026] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual void octomap_server::OctomapServer::handlePostNodeTraversal(const rclcpp::Time&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[25.858399] (octomap_server2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/fastlio2/octomap_ros2/src/octomap_server.cpp:1173:29:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Krostime\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[25.858676] (octomap_server2) StderrLine: {'line': b' 1173 |         \x1b[01;35m\x1b[Kconst rclcpp::Time& rostime\x1b[m\x1b[K){\n'}
[25.858916] (octomap_server2) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~^~~~~~~\x1b[m\x1b[K\n'}
[25.956664] (-) TimerEvent: {}
[26.057484] (-) TimerEvent: {}
[26.158174] (-) TimerEvent: {}
[26.258847] (-) TimerEvent: {}
[26.359531] (-) TimerEvent: {}
[26.460793] (-) TimerEvent: {}
[26.561496] (-) TimerEvent: {}
[26.662231] (-) TimerEvent: {}
[26.763001] (-) TimerEvent: {}
[26.863678] (-) TimerEvent: {}
[26.964540] (-) TimerEvent: {}
[27.065891] (-) TimerEvent: {}
[27.166610] (-) TimerEvent: {}
[27.267303] (-) TimerEvent: {}
[27.367990] (-) TimerEvent: {}
[27.468807] (-) TimerEvent: {}
[27.569503] (-) TimerEvent: {}
[27.670247] (-) TimerEvent: {}
[27.771053] (-) TimerEvent: {}
[27.871898] (-) TimerEvent: {}
[27.972921] (-) TimerEvent: {}
[28.074188] (-) TimerEvent: {}
[28.175005] (-) TimerEvent: {}
[28.275828] (-) TimerEvent: {}
[28.376693] (-) TimerEvent: {}
[28.477488] (-) TimerEvent: {}
[28.578197] (-) TimerEvent: {}
[28.679002] (-) TimerEvent: {}
[28.779801] (-) TimerEvent: {}
[28.880610] (-) TimerEvent: {}
[28.981470] (-) TimerEvent: {}
[29.082224] (-) TimerEvent: {}
[29.183028] (-) TimerEvent: {}
[29.283771] (-) TimerEvent: {}
[29.384515] (-) TimerEvent: {}
[29.485384] (-) TimerEvent: {}
[29.586271] (-) TimerEvent: {}
[29.687081] (-) TimerEvent: {}
[29.787789] (-) TimerEvent: {}
[29.888602] (-) TimerEvent: {}
[29.989413] (-) TimerEvent: {}
[30.090233] (-) TimerEvent: {}
[30.191022] (-) TimerEvent: {}
[30.291832] (-) TimerEvent: {}
[30.392633] (-) TimerEvent: {}
[30.493454] (-) TimerEvent: {}
[30.594293] (-) TimerEvent: {}
[30.695017] (-) TimerEvent: {}
[30.795896] (-) TimerEvent: {}
[30.896633] (-) TimerEvent: {}
[30.997459] (-) TimerEvent: {}
[31.098311] (-) TimerEvent: {}
[31.199014] (-) TimerEvent: {}
[31.299708] (-) TimerEvent: {}
[31.400521] (-) TimerEvent: {}
[31.501343] (-) TimerEvent: {}
[31.602207] (-) TimerEvent: {}
[31.703008] (-) TimerEvent: {}
[31.803702] (-) TimerEvent: {}
[31.904429] (-) TimerEvent: {}
[32.005274] (-) TimerEvent: {}
[32.106039] (-) TimerEvent: {}
[32.206886] (-) TimerEvent: {}
[32.307682] (-) TimerEvent: {}
[32.408398] (-) TimerEvent: {}
[32.509228] (-) TimerEvent: {}
[32.610013] (-) TimerEvent: {}
[32.710712] (-) TimerEvent: {}
[32.811518] (-) TimerEvent: {}
[32.912293] (-) TimerEvent: {}
[33.012956] (-) TimerEvent: {}
[33.113685] (-) TimerEvent: {}
[33.214563] (-) TimerEvent: {}
[33.315281] (-) TimerEvent: {}
[33.416007] (-) TimerEvent: {}
[33.516732] (-) TimerEvent: {}
[33.617579] (-) TimerEvent: {}
[33.718268] (-) TimerEvent: {}
[33.819085] (-) TimerEvent: {}
[33.919905] (-) TimerEvent: {}
[34.020713] (-) TimerEvent: {}
[34.121499] (-) TimerEvent: {}
[34.222213] (-) TimerEvent: {}
[34.322884] (-) TimerEvent: {}
[34.423649] (-) TimerEvent: {}
[34.524480] (-) TimerEvent: {}
[34.625382] (-) TimerEvent: {}
[34.726126] (-) TimerEvent: {}
[34.826960] (-) TimerEvent: {}
[34.927804] (-) TimerEvent: {}
[35.028693] (-) TimerEvent: {}
[35.129527] (-) TimerEvent: {}
[35.230390] (-) TimerEvent: {}
[35.331101] (-) TimerEvent: {}
[35.431854] (-) TimerEvent: {}
[35.532611] (-) TimerEvent: {}
[35.633438] (-) TimerEvent: {}
[35.734219] (-) TimerEvent: {}
[35.835035] (-) TimerEvent: {}
[35.935862] (-) TimerEvent: {}
[36.036662] (-) TimerEvent: {}
[36.137364] (-) TimerEvent: {}
[36.238189] (-) TimerEvent: {}
[36.338910] (-) TimerEvent: {}
[36.439706] (-) TimerEvent: {}
[36.540425] (-) TimerEvent: {}
[36.641224] (-) TimerEvent: {}
[36.742107] (-) TimerEvent: {}
[36.842916] (-) TimerEvent: {}
[36.943745] (-) TimerEvent: {}
[37.044692] (-) TimerEvent: {}
[37.145405] (-) TimerEvent: {}
[37.246284] (-) TimerEvent: {}
[37.347081] (-) TimerEvent: {}
[37.447821] (-) TimerEvent: {}
[37.548675] (-) TimerEvent: {}
[37.649422] (-) TimerEvent: {}
[37.750174] (-) TimerEvent: {}
[37.851001] (-) TimerEvent: {}
[37.951814] (-) TimerEvent: {}
[38.052574] (-) TimerEvent: {}
[38.153381] (-) TimerEvent: {}
[38.254130] (-) TimerEvent: {}
[38.354972] (-) TimerEvent: {}
[38.455767] (-) TimerEvent: {}
[38.556588] (-) TimerEvent: {}
[38.657419] (-) TimerEvent: {}
[38.758250] (-) TimerEvent: {}
[38.859037] (-) TimerEvent: {}
[38.959882] (-) TimerEvent: {}
[39.060711] (-) TimerEvent: {}
[39.161507] (-) TimerEvent: {}
[39.262245] (-) TimerEvent: {}
[39.363049] (-) TimerEvent: {}
[39.463921] (-) TimerEvent: {}
[39.564928] (-) TimerEvent: {}
[39.665717] (-) TimerEvent: {}
[39.766427] (-) TimerEvent: {}
[39.867147] (-) TimerEvent: {}
[39.967886] (-) TimerEvent: {}
[40.068635] (-) TimerEvent: {}
[40.169326] (-) TimerEvent: {}
[40.270158] (-) TimerEvent: {}
[40.370896] (-) TimerEvent: {}
[40.471740] (-) TimerEvent: {}
[40.572570] (-) TimerEvent: {}
[40.673382] (-) TimerEvent: {}
[40.774194] (-) TimerEvent: {}
[40.874959] (-) TimerEvent: {}
[40.975756] (-) TimerEvent: {}
[41.076557] (-) TimerEvent: {}
[41.177356] (-) TimerEvent: {}
[41.278256] (-) TimerEvent: {}
[41.379057] (-) TimerEvent: {}
[41.479836] (-) TimerEvent: {}
[41.580531] (-) TimerEvent: {}
[41.681739] (-) TimerEvent: {}
[41.782781] (-) TimerEvent: {}
[41.883583] (-) TimerEvent: {}
[41.984490] (-) TimerEvent: {}
[42.085342] (-) TimerEvent: {}
[42.186298] (-) TimerEvent: {}
[42.287141] (-) TimerEvent: {}
[42.388051] (-) TimerEvent: {}
[42.488904] (-) TimerEvent: {}
[42.589741] (-) TimerEvent: {}
[42.690554] (-) TimerEvent: {}
[42.791348] (-) TimerEvent: {}
[42.892159] (-) TimerEvent: {}
[42.992958] (-) TimerEvent: {}
[43.093760] (-) TimerEvent: {}
[43.194586] (-) TimerEvent: {}
[43.295366] (-) TimerEvent: {}
[43.396158] (-) TimerEvent: {}
[43.496894] (-) TimerEvent: {}
[43.597747] (-) TimerEvent: {}
[43.698496] (-) TimerEvent: {}
[43.799173] (-) TimerEvent: {}
[43.899837] (-) TimerEvent: {}
[44.000484] (-) TimerEvent: {}
[44.101211] (-) TimerEvent: {}
[44.201892] (-) TimerEvent: {}
[44.302684] (-) TimerEvent: {}
[44.403350] (-) TimerEvent: {}
[44.504142] (-) TimerEvent: {}
[44.604843] (-) TimerEvent: {}
[44.705665] (-) TimerEvent: {}
[44.806476] (-) TimerEvent: {}
[44.907177] (-) TimerEvent: {}
[45.008029] (-) TimerEvent: {}
[45.108797] (-) TimerEvent: {}
[45.209535] (-) TimerEvent: {}
[45.310370] (-) TimerEvent: {}
[45.411158] (-) TimerEvent: {}
[45.511969] (-) TimerEvent: {}
[45.612783] (-) TimerEvent: {}
[45.713562] (-) TimerEvent: {}
[45.814554] (-) TimerEvent: {}
[45.915985] (-) TimerEvent: {}
[46.016985] (-) TimerEvent: {}
[46.117947] (-) TimerEvent: {}
[46.218828] (-) TimerEvent: {}
[46.319645] (-) TimerEvent: {}
[46.420452] (-) TimerEvent: {}
[46.521268] (-) TimerEvent: {}
[46.622120] (-) TimerEvent: {}
[46.722958] (-) TimerEvent: {}
[46.823882] (-) TimerEvent: {}
[46.925108] (-) TimerEvent: {}
[47.025806] (-) TimerEvent: {}
[47.126646] (-) TimerEvent: {}
[47.227450] (-) TimerEvent: {}
[47.328290] (-) TimerEvent: {}
[47.429107] (-) TimerEvent: {}
[47.529890] (-) TimerEvent: {}
[47.630671] (-) TimerEvent: {}
[47.731411] (-) TimerEvent: {}
[47.832261] (-) TimerEvent: {}
[47.933063] (-) TimerEvent: {}
[48.033777] (-) TimerEvent: {}
[48.134613] (-) TimerEvent: {}
[48.235392] (-) TimerEvent: {}
[48.336093] (-) TimerEvent: {}
[48.436878] (-) TimerEvent: {}
[48.537541] (-) TimerEvent: {}
[48.638220] (-) TimerEvent: {}
[48.738895] (-) TimerEvent: {}
[48.839702] (-) TimerEvent: {}
[48.940439] (-) TimerEvent: {}
[49.041104] (-) TimerEvent: {}
[49.141899] (-) TimerEvent: {}
[49.242752] (-) TimerEvent: {}
[49.343512] (-) TimerEvent: {}
[49.444453] (-) TimerEvent: {}
[49.545137] (-) TimerEvent: {}
[49.645949] (-) TimerEvent: {}
[49.746778] (-) TimerEvent: {}
[49.847484] (-) TimerEvent: {}
[49.948284] (-) TimerEvent: {}
[50.048967] (-) TimerEvent: {}
[50.149684] (-) TimerEvent: {}
[50.250541] (-) TimerEvent: {}
[50.351222] (-) TimerEvent: {}
[50.452053] (-) TimerEvent: {}
[50.552844] (-) TimerEvent: {}
[50.653658] (-) TimerEvent: {}
[50.754460] (-) TimerEvent: {}
[50.855265] (-) TimerEvent: {}
[50.955943] (-) TimerEvent: {}
[51.056725] (-) TimerEvent: {}
[51.157522] (-) TimerEvent: {}
[51.258247] (-) TimerEvent: {}
[51.359026] (-) TimerEvent: {}
[51.459796] (-) TimerEvent: {}
[51.560607] (-) TimerEvent: {}
[51.661412] (-) TimerEvent: {}
[51.762247] (-) TimerEvent: {}
[51.863013] (-) TimerEvent: {}
[51.963844] (-) TimerEvent: {}
[52.064720] (-) TimerEvent: {}
[52.165503] (-) TimerEvent: {}
[52.266189] (-) TimerEvent: {}
[52.366839] (-) TimerEvent: {}
[52.467546] (-) TimerEvent: {}
[52.568331] (-) TimerEvent: {}
[52.669114] (-) TimerEvent: {}
[52.769823] (-) TimerEvent: {}
[52.870560] (-) TimerEvent: {}
[52.971348] (-) TimerEvent: {}
[53.072107] (-) TimerEvent: {}
[53.172767] (-) TimerEvent: {}
[53.273578] (-) TimerEvent: {}
[53.374363] (-) TimerEvent: {}
[53.475033] (-) TimerEvent: {}
[53.575830] (-) TimerEvent: {}
[53.676597] (-) TimerEvent: {}
[53.777263] (-) TimerEvent: {}
[53.878054] (-) TimerEvent: {}
[53.978870] (-) TimerEvent: {}
[54.079687] (-) TimerEvent: {}
[54.180591] (-) TimerEvent: {}
[54.281355] (-) TimerEvent: {}
[54.382151] (-) TimerEvent: {}
[54.482799] (-) TimerEvent: {}
[54.583473] (-) TimerEvent: {}
[54.684273] (-) TimerEvent: {}
[54.785084] (-) TimerEvent: {}
[54.885888] (-) TimerEvent: {}
[54.986705] (-) TimerEvent: {}
[55.087497] (-) TimerEvent: {}
[55.188318] (-) TimerEvent: {}
[55.289406] (-) TimerEvent: {}
[55.390252] (-) TimerEvent: {}
[55.490935] (-) TimerEvent: {}
[55.591871] (-) TimerEvent: {}
[55.692673] (-) TimerEvent: {}
[55.793665] (-) TimerEvent: {}
[55.894500] (-) TimerEvent: {}
[55.995310] (-) TimerEvent: {}
[56.096151] (-) TimerEvent: {}
[56.196999] (-) TimerEvent: {}
[56.297868] (-) TimerEvent: {}
[56.398753] (-) TimerEvent: {}
[56.499454] (-) TimerEvent: {}
[56.600218] (-) TimerEvent: {}
[56.701009] (-) TimerEvent: {}
[56.801838] (-) TimerEvent: {}
[56.902697] (-) TimerEvent: {}
[57.003630] (-) TimerEvent: {}
[57.104459] (-) TimerEvent: {}
[57.205257] (-) TimerEvent: {}
[57.305947] (-) TimerEvent: {}
[57.406795] (-) TimerEvent: {}
[57.507569] (-) TimerEvent: {}
[57.608337] (-) TimerEvent: {}
[57.709149] (-) TimerEvent: {}
[57.809932] (-) TimerEvent: {}
[57.910653] (-) TimerEvent: {}
[58.011463] (-) TimerEvent: {}
[58.112277] (-) TimerEvent: {}
[58.213025] (-) TimerEvent: {}
[58.313881] (-) TimerEvent: {}
[58.414736] (-) TimerEvent: {}
[58.515447] (-) TimerEvent: {}
[58.616159] (-) TimerEvent: {}
[58.716885] (-) TimerEvent: {}
[58.817698] (-) TimerEvent: {}
[58.918442] (-) TimerEvent: {}
[59.019280] (-) TimerEvent: {}
[59.120108] (-) TimerEvent: {}
[59.220899] (-) TimerEvent: {}
[59.321652] (-) TimerEvent: {}
[59.422525] (-) TimerEvent: {}
[59.523560] (-) TimerEvent: {}
[59.624638] (-) TimerEvent: {}
[59.725974] (-) TimerEvent: {}
[59.826804] (-) TimerEvent: {}
[59.927614] (-) TimerEvent: {}
[60.028549] (-) TimerEvent: {}
[60.129444] (-) TimerEvent: {}
[60.231529] (-) TimerEvent: {}
[60.332375] (-) TimerEvent: {}
[60.433190] (-) TimerEvent: {}
[60.533869] (-) TimerEvent: {}
[60.634554] (-) TimerEvent: {}
[60.735230] (-) TimerEvent: {}
[60.836020] (-) TimerEvent: {}
[60.936849] (-) TimerEvent: {}
[61.037993] (-) TimerEvent: {}
[61.138692] (-) TimerEvent: {}
[61.239391] (-) TimerEvent: {}
[61.313692] (octomap_server2) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX shared library liboctomap_server2.so\x1b[0m\n'}
[61.339588] (-) TimerEvent: {}
[61.440369] (-) TimerEvent: {}
[61.541181] (-) TimerEvent: {}
[61.642001] (-) TimerEvent: {}
[61.742861] (-) TimerEvent: {}
[61.843720] (-) TimerEvent: {}
[61.944615] (-) TimerEvent: {}
[62.045450] (-) TimerEvent: {}
[62.146271] (-) TimerEvent: {}
[62.247116] (-) TimerEvent: {}
[62.348005] (-) TimerEvent: {}
[62.448855] (-) TimerEvent: {}
[62.549671] (-) TimerEvent: {}
[62.650499] (-) TimerEvent: {}
[62.751313] (-) TimerEvent: {}
[62.852151] (-) TimerEvent: {}
[62.952960] (-) TimerEvent: {}
[63.053779] (-) TimerEvent: {}
[63.154669] (-) TimerEvent: {}
[63.255485] (-) TimerEvent: {}
[63.356299] (-) TimerEvent: {}
[63.457119] (-) TimerEvent: {}
[63.557966] (-) TimerEvent: {}
[63.658810] (-) TimerEvent: {}
[63.759625] (-) TimerEvent: {}
[63.860458] (-) TimerEvent: {}
[63.961319] (-) TimerEvent: {}
[64.062162] (-) TimerEvent: {}
[64.162973] (-) TimerEvent: {}
[64.263843] (-) TimerEvent: {}
[64.364660] (-) TimerEvent: {}
[64.465518] (-) TimerEvent: {}
[64.566404] (-) TimerEvent: {}
[64.667307] (-) TimerEvent: {}
[64.768160] (-) TimerEvent: {}
[64.869059] (-) TimerEvent: {}
[64.969942] (-) TimerEvent: {}
[65.070821] (-) TimerEvent: {}
[65.171673] (-) TimerEvent: {}
[65.272911] (-) TimerEvent: {}
[65.373749] (-) TimerEvent: {}
[65.474605] (-) TimerEvent: {}
[65.575405] (-) TimerEvent: {}
[65.676188] (-) TimerEvent: {}
[65.776975] (-) TimerEvent: {}
[65.877753] (-) TimerEvent: {}
[65.978502] (-) TimerEvent: {}
[66.081313] (-) TimerEvent: {}
[66.182169] (-) TimerEvent: {}
[66.282891] (-) TimerEvent: {}
[66.383716] (-) TimerEvent: {}
[66.484400] (-) TimerEvent: {}
[66.585211] (-) TimerEvent: {}
[66.686024] (-) TimerEvent: {}
[66.769815] (octomap_server2) StdoutLine: {'line': b'[100%] Built target octomap_server2\n'}
[66.786227] (-) TimerEvent: {}
[66.801326] (octomap_server2) CommandEnded: {'returncode': 0}
[66.811105] (octomap_server2) JobProgress: {'identifier': 'octomap_server2', 'progress': 'install'}
[66.829214] (octomap_server2) Command: {'cmd': ['/usr/local/bin/cmake', '--install', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'], 'cwd': '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'nvidia'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('JETSON_L4T', '36.3.0'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin NX Engineering Reference Developer Kit'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1734'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'nvidia'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (8GB ram)'), ('JETSON_SERIAL_NUMBER', '1421224256350'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'nvidia'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/nvidia-desktop:@/tmp/.ICE-unix/1734,unix/nvidia-desktop:/tmp/.ICE-unix/1734'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/414342bb_5c5b_40d3_b637_5d3c77877ad3'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('GNOME_TERMINAL_SERVICE', ':1.126'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/fastlio2/octomap_ros2/build/octomap_server2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=ca8100504d432901ed7d0ab40000003e'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.0'), ('CMAKE_PREFIX_PATH', '/home/<USER>/fastlio2/ws_livox/install/livox_ros_driver2:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0003')]), 'shell': False}
[66.843013] (octomap_server2) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[66.844674] (octomap_server2) StdoutLine: {'line': b'-- Execute custom install script\n'}
[66.846082] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/octomap_server2/octomap_server\n'}
[66.851290] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/lib/liboctomap_server2.so\n'}
[66.858826] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2//launch/octomap_server_launch.py\n'}
[66.863272] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/package_run_dependencies/octomap_server2\n'}
[66.868095] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/parent_prefix_path/octomap_server2\n'}
[66.873375] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.sh\n'}
[66.878367] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/ament_prefix_path.dsv\n'}
[66.883328] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.sh\n'}
[66.886298] (-) TimerEvent: {}
[66.887284] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/environment/path.dsv\n'}
[66.892329] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.bash\n'}
[66.897612] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.sh\n'}
[66.902452] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.zsh\n'}
[66.906904] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/local_setup.dsv\n'}
[66.911985] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.dsv\n'}
[66.920498] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/packages/octomap_server2\n'}
[66.928391] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/ament_index/resource_index/rclcpp_components/octomap_server2\n'}
[66.935468] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config.cmake\n'}
[66.943397] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/cmake/octomap_server2Config-version.cmake\n'}
[66.950456] (octomap_server2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/fastlio2/octomap_ros2/install/octomap_server2/share/octomap_server2/package.xml\n'}
[66.961210] (octomap_server2) CommandEnded: {'returncode': 0}
[66.987463] (-) TimerEvent: {}
[67.050086] (octomap_server2) JobEnded: {'identifier': 'octomap_server2', 'rc': 0}
[67.053171] (-) EventReactorShutdown: {}
