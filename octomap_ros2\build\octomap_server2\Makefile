# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/fastlio2/octomap_ros2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/local/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/fastlio2/octomap_ros2/build/octomap_server2/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named octomap_server2_uninstall

# Build rule for target.
octomap_server2_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 octomap_server2_uninstall
.PHONY : octomap_server2_uninstall

# fast build rule for target.
octomap_server2_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2_uninstall.dir/build.make CMakeFiles/octomap_server2_uninstall.dir/build
.PHONY : octomap_server2_uninstall/fast

#=============================================================================
# Target rules for targets named octomap_server2

# Build rule for target.
octomap_server2: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 octomap_server2
.PHONY : octomap_server2

# fast build rule for target.
octomap_server2/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/build
.PHONY : octomap_server2/fast

#=============================================================================
# Target rules for targets named octomap_server

# Build rule for target.
octomap_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 octomap_server
.PHONY : octomap_server

# fast build rule for target.
octomap_server/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server.dir/build.make CMakeFiles/octomap_server.dir/build
.PHONY : octomap_server/fast

rclcpp_components/node_main_octomap_server.o: rclcpp_components/node_main_octomap_server.cpp.o
.PHONY : rclcpp_components/node_main_octomap_server.o

# target to build an object file
rclcpp_components/node_main_octomap_server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server.dir/build.make CMakeFiles/octomap_server.dir/rclcpp_components/node_main_octomap_server.cpp.o
.PHONY : rclcpp_components/node_main_octomap_server.cpp.o

rclcpp_components/node_main_octomap_server.i: rclcpp_components/node_main_octomap_server.cpp.i
.PHONY : rclcpp_components/node_main_octomap_server.i

# target to preprocess a source file
rclcpp_components/node_main_octomap_server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server.dir/build.make CMakeFiles/octomap_server.dir/rclcpp_components/node_main_octomap_server.cpp.i
.PHONY : rclcpp_components/node_main_octomap_server.cpp.i

rclcpp_components/node_main_octomap_server.s: rclcpp_components/node_main_octomap_server.cpp.s
.PHONY : rclcpp_components/node_main_octomap_server.s

# target to generate assembly for a file
rclcpp_components/node_main_octomap_server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server.dir/build.make CMakeFiles/octomap_server.dir/rclcpp_components/node_main_octomap_server.cpp.s
.PHONY : rclcpp_components/node_main_octomap_server.cpp.s

src/conversions.o: src/conversions.cpp.o
.PHONY : src/conversions.o

# target to build an object file
src/conversions.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/conversions.cpp.o
.PHONY : src/conversions.cpp.o

src/conversions.i: src/conversions.cpp.i
.PHONY : src/conversions.i

# target to preprocess a source file
src/conversions.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/conversions.cpp.i
.PHONY : src/conversions.cpp.i

src/conversions.s: src/conversions.cpp.s
.PHONY : src/conversions.s

# target to generate assembly for a file
src/conversions.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/conversions.cpp.s
.PHONY : src/conversions.cpp.s

src/octomap_server.o: src/octomap_server.cpp.o
.PHONY : src/octomap_server.o

# target to build an object file
src/octomap_server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.o
.PHONY : src/octomap_server.cpp.o

src/octomap_server.i: src/octomap_server.cpp.i
.PHONY : src/octomap_server.i

# target to preprocess a source file
src/octomap_server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.i
.PHONY : src/octomap_server.cpp.i

src/octomap_server.s: src/octomap_server.cpp.s
.PHONY : src/octomap_server.s

# target to generate assembly for a file
src/octomap_server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/octomap_server.cpp.s
.PHONY : src/octomap_server.cpp.s

src/transforms.o: src/transforms.cpp.o
.PHONY : src/transforms.o

# target to build an object file
src/transforms.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/transforms.cpp.o
.PHONY : src/transforms.cpp.o

src/transforms.i: src/transforms.cpp.i
.PHONY : src/transforms.i

# target to preprocess a source file
src/transforms.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/transforms.cpp.i
.PHONY : src/transforms.cpp.i

src/transforms.s: src/transforms.cpp.s
.PHONY : src/transforms.s

# target to generate assembly for a file
src/transforms.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/octomap_server2.dir/build.make CMakeFiles/octomap_server2.dir/src/transforms.cpp.s
.PHONY : src/transforms.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... octomap_server2_uninstall"
	@echo "... uninstall"
	@echo "... octomap_server"
	@echo "... octomap_server2"
	@echo "... rclcpp_components/node_main_octomap_server.o"
	@echo "... rclcpp_components/node_main_octomap_server.i"
	@echo "... rclcpp_components/node_main_octomap_server.s"
	@echo "... src/conversions.o"
	@echo "... src/conversions.i"
	@echo "... src/conversions.s"
	@echo "... src/octomap_server.o"
	@echo "... src/octomap_server.i"
	@echo "... src/octomap_server.s"
	@echo "... src/transforms.o"
	@echo "... src/transforms.i"
	@echo "... src/transforms.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

