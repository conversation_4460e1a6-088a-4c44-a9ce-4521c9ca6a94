#include "grid_map_processor.h"
#include <vector>
#include <algorithm>
#include <queue>

namespace fastlio
{

GridMapProcessor::GridMapProcessor(const Config& config) : m_config(config)
{
    
 
    // 确保分辨率正确
    std::cout << "Resolution: " << m_config.resolution << std::endl;

    // 初始化全局地图参数
    m_global_width = static_cast<int>(m_config.global_size / m_config.resolution);
    m_global_height = static_cast<int>(m_config.global_size / m_config.resolution);
    m_global_map_data.resize(m_global_width * m_global_height, -1);  // 初始化为未知状态(-1)
    m_observed_count.resize(m_global_width * m_global_height, 0);    // 初始化观测次数为0
  
    
    // 初始化局部地图参数
    m_local_width = static_cast<int>(m_config.local_size / m_config.resolution);
    m_local_height = static_cast<int>(m_config.local_size / m_config.resolution);
    m_local_map_data.resize(m_local_width * m_local_height, -1);     // 初始化为未知状态(-1)
    
    m_min_z = std::numeric_limits<double>::max();
    m_max_z = std::numeric_limits<double>::lowest();
    m_map_origin = Eigen::Vector2d::Zero();
}

void GridMapProcessor::updateMap(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud, const Eigen::Vector3d& position)
{
    if (!m_config.enable_flag || !cloud || cloud->empty())
        return;

    // 更新地图原点（以机器人当前位置为中心）
    m_map_origin.x() = -m_config.global_size / 2;
    m_map_origin.y() = -m_config.global_size / 2;

    // 清空地图数据（初始化为未知状态）
    // std::fill(m_global_map_data.begin(), m_global_map_data.end(), -1);
    std::fill(m_local_map_data.begin(), m_local_map_data.end(), -1);

    // 处理每个点云点
    for (const auto& point : cloud->points)
    {
        // 更新高度范围
        m_min_z = std::min(m_min_z, static_cast<double>(point.z));
        m_max_z = std::max(m_max_z, static_cast<double>(point.z));

        // 更新栅格
        updateCell(point);
    }
}

nav_msgs::msg::OccupancyGrid::SharedPtr GridMapProcessor::get2DGridMap() const
{
    auto grid_map = std::make_shared<nav_msgs::msg::OccupancyGrid>();
    
    // 设置地图基本信息
    grid_map->header.frame_id = "odom";  // 修改为odom坐标系
    grid_map->header.stamp = rclcpp::Time();  // 使用当前时间
    grid_map->info.resolution = m_config.resolution;
    grid_map->info.width = m_global_width;
    grid_map->info.height = m_global_height;
    std::cout << "Grid Map Resolution: " << m_config.resolution << std::endl;
    
    // 设置地图原点
    grid_map->info.origin.position.x = m_map_origin.x();
    grid_map->info.origin.position.y = m_map_origin.y();
    grid_map->info.origin.position.z = 0.0;  // 将地图放在地面
    grid_map->info.origin.orientation.w = 1.0;
    grid_map->info.origin.orientation.x = 0.0;
    grid_map->info.origin.orientation.y = 0.0;
    grid_map->info.origin.orientation.z = 0.0;
    
    // 设置地图数据
    std::vector<int8_t> display_data(m_global_map_data.size(), -1);
    // 1. 先拷贝障碍物和空闲区
    for (size_t i = 0; i < display_data.size(); ++i) {
        if (m_global_map_data[i] == 100) {
            display_data[i] = 100; // 障碍物
        } else if (m_observed_count[i] > 0 && m_global_map_data[i] == 0) {
            display_data[i] = 0;   // 空闲
        } else {
            display_data[i] = -1;  // 未观测
        }
    }
    // 2. 膨胀障碍物（1格）
    std::vector<int8_t> dilated_data = display_data;
    int dx[8] = {-1, 0, 1, 0, -1, -1, 1, 1};
    int dy[8] = {0, 1, 0, -1, -1, 1, -1, 1};
    for (int y = 0; y < m_global_height; ++y) {
        for (int x = 0; x < m_global_width; ++x) {
            int idx = y * m_global_width + x;
            if (display_data[idx] == 100) {
                for (int k = 0; k < 8; ++k) {
                    int nx = x + dx[k];
                    int ny = y + dy[k];
                    if (nx >= 0 && nx < m_global_width && ny >= 0 && ny < m_global_height) {
                        int nidx = ny * m_global_width + nx;
                        if (display_data[nidx] != 100) {
                            dilated_data[nidx] = 100;
                        }
                    }
                }
            }
        }
    }
    // 3. flood fill填充空闲区（只保留与机器人当前位置连通的空闲区，其余设为未观测）
    std::vector<bool> visited(m_global_map_data.size(), false);
    std::queue<std::pair<int, int>> q;
    // 机器人当前位置（假设在地图中心）
    int start_x = m_global_width / 2;
    int start_y = m_global_height / 2;
    if (start_x >= 0 && start_x < m_global_width && start_y >= 0 && start_y < m_global_height) {
        q.push({start_x, start_y});
        visited[start_y * m_global_width + start_x] = true;
    }
    while (!q.empty()) {
        auto [x, y] = q.front(); q.pop();
        int idx = y * m_global_width + x;
        if (dilated_data[idx] == 100) continue; // 障碍物不填充
        dilated_data[idx] = 0; // 填充为可通行
        for (int k = 0; k < 4; ++k) {
            int nx = x + dx[k];
            int ny = y + dy[k];
            if (nx >= 0 && nx < m_global_width && ny >= 0 && ny < m_global_height) {
                int nidx = ny * m_global_width + nx;
                if (!visited[nidx] && dilated_data[nidx] != 100) {
                    visited[nidx] = true;
                    q.push({nx, ny});
                }
            }
        }
    }
    // 其余未被填充的空闲区设为未观测
    for (size_t i = 0; i < dilated_data.size(); ++i) {
        if (dilated_data[i] != 100 && !visited[i]) {
            dilated_data[i] = -1;
        }
    }
    grid_map->data = dilated_data;
    return grid_map;
}

bool GridMapProcessor::isGroundPoint(const pcl::PointXYZINormal& point) const
{
    // 根据高度和强度判断是否为地面点
    double height_ratio = (point.z - m_min_z) / (m_max_z - m_min_z);
    return height_ratio < m_config.ground_rate;
}

// Bresenham算法，返回从(x0, y0)到(x1, y1)的所有格子索引
std::vector<std::pair<int, int>> bresenhamLine(int x0, int y0, int x1, int y1) {
    std::vector<std::pair<int, int>> line;
    int dx = std::abs(x1 - x0), sx = x0 < x1 ? 1 : -1;
    int dy = -std::abs(y1 - y0), sy = y0 < y1 ? 1 : -1;
    int err = dx + dy, e2;
    int x = x0, y = y0;
    while (true) {
        line.emplace_back(x, y);
        if (x == x1 && y == y1) break;
        e2 = 2 * err;
        if (e2 >= dy) { err += dy; x += sx; }
        if (e2 <= dx) { err += dx; y += sy; }
    }
    return line;
}


void GridMapProcessor::updateCell(const pcl::PointXYZINormal& point)
{
    // 激光原点（可根据实际情况调整）
    double origin_x = 0.0;
    double origin_y = 0.0;
    int x0 = static_cast<int>((origin_x - m_map_origin.x()) / m_config.resolution);
    int y0 = static_cast<int>((origin_y - m_map_origin.y()) / m_config.resolution);
    int x1 = static_cast<int>((point.x - m_map_origin.x()) / m_config.resolution);
    int y1 = static_cast<int>((point.y - m_map_origin.y()) / m_config.resolution);
    // 检查终点是否在地图范围内
    if (x1 < 0 || x1 >= m_global_width || y1 < 0 || y1 >= m_global_height)
        return;
        
    // 获取路径上的所有格子
    auto line = bresenhamLine(x0, y0, x1, y1);
    for (size_t i = 0; i + 1 < line.size(); ++i) { // 不包括终点
        int x = line[i].first;
        int y = line[i].second;
        if (x < 0 || x >= m_global_width || y < 0 || y >= m_global_height) continue;
        int index = y * m_global_width + x;
        m_observed_count[index]++;
        // 只标记为可通行（0），不覆盖障碍物
        if (m_global_map_data[index] != 100)
            m_global_map_data[index] = 0;
    }
    // 终点格子标记为障碍物
    int end_index = y1 * m_global_width + x1;
    m_observed_count[end_index]++;
    m_global_map_data[end_index] = 100;
    
    // 更新局部地图
    int local_x = static_cast<int>((point.x - (m_map_origin.x() + m_config.global_size/2 - m_config.local_size/2)) / m_config.resolution);
    int local_y = static_cast<int>((point.y - (m_map_origin.y() + m_config.global_size/2 - m_config.local_size/2)) / m_config.resolution);
    
    if (local_x >= 0 && local_x < m_local_width && local_y >= 0 && local_y < m_local_height)
    {
        int local_index = local_y * m_local_width + local_x;
        m_local_map_data[local_index] = m_global_map_data[end_index];
    }
}

} // namespace fastlio 
