#include "grid_map_processor.h"
#include <iostream>

namespace fastlio
{

GridMapProcessor::GridMapProcessor(const Config& config) : m_config(config)
{
    std::cout << "Initializing Point Cloud Processor with voxel size: " << m_config.voxel_size << std::endl;

    // 初始化点云存储
    m_ground_cloud = pcl::PointCloud<pcl::PointXYZINormal>::Ptr(new pcl::PointCloud<pcl::PointXYZINormal>);
    m_non_ground_cloud = pcl::PointCloud<pcl::PointXYZINormal>::Ptr(new pcl::PointCloud<pcl::PointXYZINormal>);

    // 配置RANSAC分割器
    m_seg.setOptimizeCoefficients(true);
    m_seg.setModelType(pcl::SACMODEL_PLANE);
    m_seg.setMethodType(pcl::SAC_RANSAC);
    m_seg.setMaxIterations(m_config.max_iterations);
    m_seg.setDistanceThreshold(m_config.plane_distance_threshold);

    // 配置欧式聚类
    m_ec.setClusterTolerance(m_config.cluster_tolerance);
    m_ec.setMinClusterSize(m_config.min_cluster_size);
    m_ec.setMaxClusterSize(m_config.max_cluster_size);

    // 配置体素滤波器
    m_voxel_filter.setLeafSize(m_config.voxel_size, m_config.voxel_size, m_config.voxel_size);
}

void GridMapProcessor::processPointCloud(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud, const Eigen::Vector3d& position)
{
    if (!m_config.enable_flag || !cloud || cloud->empty())
        return;

    std::cout << "Processing point cloud with " << cloud->size() << " points" << std::endl;

    // 清空之前的结果
    m_ground_cloud->clear();
    m_non_ground_cloud->clear();
    m_cluster_clouds.clear();

    // 1. 体素滤波降采样
    auto filtered_cloud = applyVoxelFilter(cloud);
    std::cout << "After voxel filtering: " << filtered_cloud->size() << " points" << std::endl;

    // 2. RANSAC平面分割
    performRANSACSegmentation(filtered_cloud);
    std::cout << "Ground points: " << m_ground_cloud->size() << ", Non-ground points: " << m_non_ground_cloud->size() << std::endl;

    // 3. 对非地面点进行欧式聚类
    if (!m_non_ground_cloud->empty())
    {
        performEuclideanClustering(m_non_ground_cloud);
        std::cout << "Found " << m_cluster_clouds.size() << " clusters" << std::endl;
    }
}

pcl::PointCloud<pcl::PointXYZINormal>::Ptr GridMapProcessor::applyVoxelFilter(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud)
{
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZINormal>);

    m_voxel_filter.setInputCloud(cloud);
    m_voxel_filter.filter(*filtered_cloud);

    return filtered_cloud;
}

void GridMapProcessor::performRANSACSegmentation(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud)
{
    pcl::ModelCoefficients::Ptr coefficients(new pcl::ModelCoefficients);
    pcl::PointIndices::Ptr inliers(new pcl::PointIndices);

    // 设置输入点云
    m_seg.setInputCloud(cloud);

    // 执行分割
    m_seg.segment(*inliers, *coefficients);

    if (inliers->indices.size() == 0)
    {
        std::cout << "Could not estimate a planar model for the given dataset." << std::endl;
        *m_non_ground_cloud = *cloud;  // 如果没有找到平面，所有点都作为非地面点
        return;
    }

    // 检查平面法向量，判断是否为地面
    // 地面的法向量应该接近垂直向上 (0, 0, 1)
    Eigen::Vector3f normal(coefficients->values[0], coefficients->values[1], coefficients->values[2]);
    normal.normalize();

    // 计算与垂直向上方向的夹角
    float angle = std::acos(std::abs(normal.z()));
    float angle_threshold = M_PI / 6;  // 30度

    // 提取地面和非地面点
    pcl::ExtractIndices<pcl::PointXYZINormal> extract;
    extract.setInputCloud(cloud);
    extract.setIndices(inliers);

    if (angle < angle_threshold)  // 如果是接近水平的平面，认为是地面
    {
        // 提取地面点
        extract.setNegative(false);
        extract.filter(*m_ground_cloud);

        // 提取非地面点
        extract.setNegative(true);
        extract.filter(*m_non_ground_cloud);
    }
    else  // 如果不是水平平面，所有点都作为非地面点
    {
        *m_non_ground_cloud = *cloud;
    }
}

void GridMapProcessor::performEuclideanClustering(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr& cloud)
{
    // 创建KdTree用于搜索
    pcl::search::KdTree<pcl::PointXYZINormal>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZINormal>);
    tree->setInputCloud(cloud);

    // 存储聚类结果的索引
    std::vector<pcl::PointIndices> cluster_indices;

    // 设置聚类参数
    m_ec.setSearchMethod(tree);
    m_ec.setInputCloud(cloud);

    // 执行聚类
    m_ec.extract(cluster_indices);

    // 清空之前的聚类结果
    m_cluster_clouds.clear();

    // 提取每个聚类
    for (const auto& indices : cluster_indices)
    {
        pcl::PointCloud<pcl::PointXYZINormal>::Ptr cluster_cloud(new pcl::PointCloud<pcl::PointXYZINormal>);

        for (const auto& index : indices.indices)
        {
            cluster_cloud->points.push_back(cloud->points[index]);
        }

        cluster_cloud->width = cluster_cloud->points.size();
        cluster_cloud->height = 1;
        cluster_cloud->is_dense = true;

        m_cluster_clouds.push_back(cluster_cloud);
    }

    std::cout << "Euclidean clustering found " << m_cluster_clouds.size() << " clusters" << std::endl;
}


pcl::PointCloud<pcl::PointXYZINormal>::Ptr GridMapProcessor::getGroundCloud() const
{
    return m_ground_cloud;
}

pcl::PointCloud<pcl::PointXYZINormal>::Ptr GridMapProcessor::getNonGroundCloud() const
{
    return m_non_ground_cloud;
}

std::vector<pcl::PointCloud<pcl::PointXYZINormal>::Ptr> GridMapProcessor::getClusterClouds() const
{
    return m_cluster_clouds;
}

} // namespace fastlio
